.product{
 margin-top: 90px;
.main {
  padding-top: 30px;
  margin: auto;
  h2{
      text-align: center;
      font-size: 60px;
      font-weight: 700;
      color: $color-brand;
      text-transform: uppercase;
      padding-bottom: 30px;
      @media screen and(max-width:768px){
        font-size: 30px;
           }
  }
  .categories{
    display: flex;
    justify-content: center;
    @media screen and(max-width:768px){
    display: block;
           }
  }
  }
  h1 {
    font-size: 50px;
    word-break: break-all;
  }
  .row {
    margin: 10px -16px;
  }
  /* Add padding BETWEEN each column */
  .row,
  .row > .column {
    padding: 8px;
  }
  /* Create three equal columns that floats next to each other */
  .column {
    float: left;
    width: 33.33%;
    display: none; /* Hide all elements by default */
  }
  /* Clear floats after rows */ 
  .row:after {
    content: "";
    display: table;
    clear: both;
  }
  /* Content */
  .content {
   padding: 50px 0;
   .img_zoom{
    overflow: hidden;
   }
    .wrapper{
        max-width: 330px;
        position: relative;
             &:hover{
            .img_details{
                position: absolute;
                left: 44%;
                display: block;
bottom: 45%;
                z-index: 3;
                @media screen and(max-width:992px){
                    left: 35%;
bottom: 43%;
                }
                @media screen and(max-width:992px){
                    left: 44%;
bottom: 43%;
                }
                i{
                    font-size: 25px;
                   color: $color-white;
                   padding: 4px 2px;

                   @media screen and(max-width:992px){
                  font-size: 12px;
                }

                }
            }
            &:after{
        position: absolute;
        content: " ";
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        background:#00000054 !important
    }
    }
    &:before{
        position: absolute;
        content: " ";
        display: block;
        top: 50%;
        left: 50%;
    }
        @media screen and(max-width:576px){
            max-width: none;
        }
.img_details{
    display: none;
}
        img{
            width: 100%;
        }
    }
    p{
        color: $color-font;
        font-size: 25px;
        font-weight: lighter;
        margin-top: 20px;
        margin-bottom: 5px;
    }
    h6{
        color: $color-brand;
        line-height: 1.5;
        font-weight: 400;
        font-size: 15px;
        letter-spacing: 1px;
        margin-bottom: 40px;
    }
    a{
        font-size: 13px;
font-weight: 700;
color: $color-brand2;
text-decoration: none;
&:hover{
    color: $color-brand;
}
    }
    #zoomIn{
        transform: scale(1);
        transition: .3s ease-in-out;
        &:hover{
            transform: scale(1.3);
            border-radius: 6px 6px 0px 0px;
    }
    }
  }
  /* The "show" class is added to the filtered elements */
  .show {
    display: block;
  }
  /* Style the buttons */
  .btn {
    border: none;
    outline: none;
    padding: 12px 16px;
    background-color: white;
    cursor: pointer;
    color: $color-font;
    font-weight: 700;
  }
  .btn:hover {
    background-color: #ddd;
  }
  .btn.active {
    color: $color-brand;
  }
}
.modal-content {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0;
    outline: 0;
    margin: auto;
    @media screen and(max-width:768px) {
margin: auto;
max-width: 65%;  
    }
    @media screen and(max-width:576px) {
        margin: auto;
        max-width: 80%;  
            }
}
.modal-dialog {
    max-width: 660px;
    margin: 1.75rem auto;
    @media screen and(max-width:768px) {
        margin: 50px 50px;
    }
}
.modal{
    .model-body{
        padding: 20px 25px;
    }
   .wrapper{
       max-width: 100% !important;
       img{
           width: 100%;
       }
   }
   .model_details{
       h4{
           color: $color-brand;
           font-size: 22px;
           font-weight: 700;
           padding-bottom: 10px;
       }
       .model_rate{
           ul{
               padding-left: 0;
               display: flex;
               span{
                   color:#ffcd07;
               }
           }
       }
       .price{
           color: $color-brand;
           font-size: 20px;
           font-weight: 700;
           span{
               margin-left: 5px;
               font-weight: 700;
               font-size: 16;
               color: $color-grey;
               text-decoration: line-through;
           }
       }
       p{
           color: $color-brand;
           line-height: 24px;
       }
       a{
           text-decoration: underline;
       }
       .product_count{
           margin-top: 30px;
           display: flex;
           @media screen and(max-width:412px) {
            display: grid !important;
            grid-row-gap: 20px !important;
           }
        #myform {
            margin-right:10px ;
            text-align: center;
            // padding: 5px;
            // border: 1px solid #ccc;
            display: flex;
            // margin: 2%;
            // border-radius: 9px;
            width: 100px;
        }
        .qty {
            width: 40px;
            height: 25px;
            text-align: center;
            border: none;
        }
        input.qtyplus { width:25px; height:25px;
        background-color: transparent;
        border: none;
        }
        input.qtyminus { width:25px; height:25px; 
            background-color: transparent;
            border: none;
        }
        .icons{
            margin-left: 15px;
            span{
                background: $color-brand2;
                color: $color-white;
                font-size: 16px;
                padding: 9px 10px;
                &:hover{
                    background-color: $color-black;
                }
                &:last-child{
                    background-color: #00bcd4 !important;
                    margin-left: 15px;
                }
            }
        }
       }
       .share{
           font-size: 14px;
           font-weight: 400;
           padding-top: 20px;
       }
       .footer_icon{
        padding-top: 5px;
        a{
            color: $color-font;
            margin-right: 15px;
            &:hover{
               color:$color-black;
            }
        }
             }
   }
}

// Product Fleet Section - Localiza inspired
.ryrox_product_fleet {
    background: $color-white;

    .fleet_title {
        font-size: 2.5rem;
        font-weight: 700;
        color: $color-brand;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
            font-size: 2rem;
        }
    }

    .fleet_subtitle {
        font-size: 1.2rem;
        color: $color-font;
        margin-bottom: 2rem;
    }

    .btn-view-all {
        border: 2px solid $color-brand2;
        color: $color-brand2;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;

        &:hover {
            background: $color-brand2;
            color: $color-white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba($color-brand2, 0.3);
        }
    }

    .product_fleet_card {
        background: $color-white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        height: 100%;

        &:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);

            .fleet_image img {
                transform: scale(1.05);
            }
        }

        .fleet_image {
            position: relative;
            height: 250px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            .fleet_overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
            }

            .fleet_badge {
                position: absolute;
                top: 1rem;
                right: 1rem;
                background: $color-brand2;
                color: $color-white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;

                &.popular {
                    background: $color-hover;
                }

                &.executive {
                    background: #6c757d;
                }
            }
        }

        .fleet_content {
            padding: 2rem;

            .fleet_category {
                font-size: 1.3rem;
                font-weight: 600;
                color: $color-brand;
                margin-bottom: 1rem;
            }

            .fleet_description {
                color: $color-font;
                line-height: 1.6;
                margin-bottom: 1.5rem;
                font-size: 0.95rem;
            }

            .fleet_features {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 1.5rem;
                flex-wrap: wrap;

                .feature_tag {
                    background: rgba($color-brand2, 0.1);
                    color: $color-brand2;
                    padding: 0.25rem 0.75rem;
                    border-radius: 15px;
                    font-size: 0.8rem;
                    font-weight: 500;
                }
            }

            .fleet_price {
                margin-bottom: 1.5rem;

                .price_from {
                    display: block;
                    font-size: 0.9rem;
                    color: $color-font;
                    margin-bottom: 0.25rem;
                }

                .price_value {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: $color-brand2;
                }
            }

            .btn-fleet {
                width: 100%;
                background: $color-brand2;
                border: none;
                color: $color-white;
                padding: 0.75rem;
                border-radius: 8px;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.3s ease;

                &:hover {
                    background: darken($color-brand2, 10%);
                    color: $color-white;
                    transform: translateY(-2px);
                }
            }
        }
    }

    @media (max-width: 768px) {
        .product_fleet_card {
            margin-bottom: 2rem;
        }
    }
}