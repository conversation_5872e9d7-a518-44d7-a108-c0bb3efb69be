# Guia de Solução de Problemas - Tema Ryrox

## Erro QWeb Cache (KeyError)

### Sintomas
```
KeyError: ('ir.qweb', <function IrQWeb._get_cached_values at 0x...>, ...)
```

### Causas Comuns
1. Cache do QWeb corrompido após modificações nos templates
2. Referências incorretas entre templates
3. Sintaxe XML inválida
4. Módulo não atualizado após alterações

### Soluções

#### 1. Limpeza de Cache
```bash
# Execute o script de limpeza
python clear_cache.py

# Ou manualmente remova:
rm -rf addons/theme_ryrox/__pycache__
rm -rf addons/theme_ryrox/*/__pycache__
find addons/theme_ryrox -name "*.pyc" -delete
```

#### 2. Reiniciar Odoo com Atualização
```bash
# Pare o servidor <PERSON> (Ctrl+C)

# Reinicie com atualização do módulo
python odoo-bin -u theme_ryrox -d sua_database

# Ou com modo desenvolvimento
python odoo-bin --dev=reload,qweb,xml -d sua_database

# Para desenvolvimento completo
python odoo-bin --dev=all -d sua_database
```

#### 3. Verificar Templates
```bash
# Execute o script de verificação
python fix_template_references.py
```

#### 4. Reinstalar Módulo
```bash
# No modo desenvolvedor do Odoo:
# 1. Vá para Apps
# 2. Remova o filtro "Apps"
# 3. Procure por "Theme Ryrox"
# 4. Clique em "Uninstall"
# 5. Reinstale o módulo
```

## Problemas de CSS

### CSS não carregando
1. Verifique se os arquivos SCSS estão sendo compilados
2. Limpe o cache do navegador (Ctrl+F5)
3. Verifique o manifest.py para assets corretos

### Estilos não aplicados
1. Verifique a ordem de importação no _components.scss
2. Confirme se as variáveis SCSS estão definidas
3. Inspecione elementos no navegador para debug

## Problemas de Layout

### Snippets não aparecem
1. Verifique se estão registrados no snippets_templates.xml
2. Confirme se os IDs dos templates estão corretos
3. Verifique se as imagens de thumbnail existem

### Layout quebrado
1. Verifique classes Bootstrap
2. Confirme responsividade
3. Teste em diferentes navegadores

## Comandos Úteis

### Desenvolvimento
```bash
# Modo desenvolvimento completo
python odoo-bin --dev=all

# Recarregar apenas templates
python odoo-bin --dev=qweb,xml

# Atualizar módulo específico
python odoo-bin -u theme_ryrox
```

### Debug
```bash
# Log detalhado
python odoo-bin --log-level=debug

# Log apenas para templates
python odoo-bin --log-handler=odoo.addons.website:DEBUG
```

### Banco de dados
```bash
# Criar nova database para teste
python odoo-bin -d test_db --init=theme_ryrox

# Atualizar lista de módulos
python odoo-bin -d sua_db --update=all
```

## Checklist de Verificação

### Antes de reportar problemas:
- [ ] Cache limpo
- [ ] Servidor reiniciado
- [ ] Módulo atualizado
- [ ] Sintaxe XML verificada
- [ ] Referências de templates corretas
- [ ] Assets CSS compilados
- [ ] Navegador atualizado (Ctrl+F5)

### Arquivos críticos:
- [ ] `__manifest__.py` - dependências e assets
- [ ] `views/snippets/snippets_templates.xml` - registro de snippets
- [ ] `static/src/scss/_variables.scss` - variáveis de cores
- [ ] `static/src/scss/components/_components.scss` - imports

## Logs Importantes

### Localização dos logs:
- Linux: `/var/log/odoo/`
- Windows: Console do servidor
- Docker: `docker logs container_name`

### Filtrar logs relevantes:
```bash
# Filtrar erros de template
grep -i "qweb\|template" odoo.log

# Filtrar erros do tema
grep -i "theme_ryrox" odoo.log
```

## Contato para Suporte

Se os problemas persistirem:
1. Colete os logs de erro
2. Documente os passos para reproduzir
3. Inclua informações do ambiente (OS, Python, Odoo version)
4. Verifique se seguiu todos os passos deste guia
