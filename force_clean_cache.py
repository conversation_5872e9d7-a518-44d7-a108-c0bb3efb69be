#!/usr/bin/env python3
"""
Script para limpeza forçada do cache e correção de referências
"""

import os
import shutil
import glob
import subprocess
import sys

def force_clean_cache():
    """Limpeza forçada de todos os caches"""
    print("🧹 Limpeza FORÇADA do cache...")
    
    # Remove todos os __pycache__ recursivamente
    pycache_dirs = []
    for root, dirs, files in os.walk('addons/theme_ryrox'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_dirs.append(os.path.join(root, dir_name))
    
    for cache_dir in pycache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"  ✅ Removido: {cache_dir}")
        except Exception as e:
            print(f"  ❌ Erro ao remover {cache_dir}: {e}")
    
    # Remove todos os .pyc
    pyc_files = []
    for root, dirs, files in os.walk('addons/theme_ryrox'):
        for file_name in files:
            if file_name.endswith('.pyc'):
                pyc_files.append(os.path.join(root, file_name))
    
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"  ✅ Removido: {pyc_file}")
        except Exception as e:
            print(f"  ❌ Erro ao remover {pyc_file}: {e}")
    
    print("✨ Cache completamente limpo!")

def remove_old_references():
    """Remove referências antigas do cache do sistema"""
    print("\n🔄 Removendo referências antigas...")
    
    # Tenta limpar cache do sistema (Windows)
    try:
        # Limpa cache DNS
        subprocess.run(['ipconfig', '/flushdns'], capture_output=True, check=False)
        print("  ✅ Cache DNS limpo")
    except:
        pass
    
    # Remove arquivos temporários do Python
    temp_dirs = [
        os.path.expanduser('~\\AppData\\Local\\Temp'),
        os.path.expanduser('~\\AppData\\Roaming\\Python'),
        'C:\\Windows\\Temp'
    ]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                # Remove apenas arquivos .pyc do temp
                pyc_files = glob.glob(os.path.join(temp_dir, '**/*.pyc'), recursive=True)
                for pyc_file in pyc_files[:10]:  # Limita a 10 para não demorar muito
                    try:
                        os.remove(pyc_file)
                    except:
                        pass
                if pyc_files:
                    print(f"  ✅ Limpos arquivos temporários em {temp_dir}")
            except:
                pass

def create_emergency_fix():
    """Cria um template de emergência para substituir referências antigas"""
    print("\n🚑 Criando fix de emergência...")
    
    emergency_template = '''<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Emergency fix for old references -->
    <template id="s_amazing_emergency" name="Amazing Banner Emergency">
        <section class="alert alert-info">
            <div class="container">
                <h4>Template em manutenção</h4>
                <p>Este snippet está sendo atualizado. Use os novos snippets Ryrox.</p>
            </div>
        </section>
    </template>
    
    <!-- Redirect old references -->
    <template id="theme_xtream.s_amazing" name="Old Amazing Redirect">
        <t t-call="theme_ryrox.s_amazing_emergency"/>
    </template>
</odoo>'''
    
    emergency_file = 'addons/theme_ryrox/views/emergency_fix.xml'
    
    try:
        with open(emergency_file, 'w', encoding='utf-8') as f:
            f.write(emergency_template)
        print(f"  ✅ Criado: {emergency_file}")
        return True
    except Exception as e:
        print(f"  ❌ Erro ao criar fix de emergência: {e}")
        return False

def update_manifest_emergency():
    """Adiciona o fix de emergência ao manifest"""
    print("\n📋 Atualizando manifest com fix de emergência...")
    
    manifest_file = 'addons/theme_ryrox/__manifest__.py'
    
    try:
        with open(manifest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Adiciona o arquivo de emergência se não estiver presente
        if 'emergency_fix.xml' not in content:
            # Encontra a linha com testimonial.xml e adiciona depois
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'testimonial.xml' in line and line.strip().endswith(','):
                    lines.insert(i + 1, "        'views/emergency_fix.xml',")
                    break
            
            content = '\n'.join(lines)
            
            with open(manifest_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  ✅ Manifest atualizado com fix de emergência")
            return True
    except Exception as e:
        print(f"  ❌ Erro ao atualizar manifest: {e}")
        return False

def show_restart_instructions():
    """Mostra instruções detalhadas para reiniciar"""
    print("\n🔄 INSTRUÇÕES PARA REINICIAR:")
    print("=" * 50)
    print("1. PARE o servidor Odoo completamente (Ctrl+C)")
    print("2. Aguarde 10 segundos")
    print("3. Execute UM dos comandos abaixo:")
    print()
    print("   OPÇÃO A - Atualização simples:")
    print("   python odoo-bin -u theme_ryrox -d sua_database")
    print()
    print("   OPÇÃO B - Limpeza completa (RECOMENDADO):")
    print("   python odoo-bin -u theme_ryrox -d sua_database --dev=reload,qweb,xml")
    print()
    print("   OPÇÃO C - Reset completo (se nada funcionar):")
    print("   python odoo-bin -i theme_ryrox -d sua_database --dev=all")
    print()
    print("4. Aguarde o servidor inicializar completamente")
    print("5. Acesse o backend e verifique se o tema está ativo")
    print()
    print("💡 Se o erro persistir:")
    print("   - Desinstale o tema pelo backend")
    print("   - Reinicie o servidor")
    print("   - Reinstale o tema")

if __name__ == "__main__":
    print("🚨 LIMPEZA FORÇADA - Tema Ryrox")
    print("=" * 40)
    
    force_clean_cache()
    remove_old_references()
    
    # Cria fix de emergência
    if create_emergency_fix():
        update_manifest_emergency()
    
    show_restart_instructions()
    
    print("\n✨ Limpeza forçada concluída!")
    print("⚠️  IMPORTANTE: Siga as instruções de reinicialização acima!")
