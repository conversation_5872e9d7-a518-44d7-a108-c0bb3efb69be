.main_product {
    .wrapper {
        display: grid;
        grid-template-columns: 1fr 1fr;
        @media screen and(max-width:768px) {
            grid-template-columns: 1fr;
        }
        .main_left {
            position: relative;
            background-size: cover;
            width: 100%;
            background-repeat: no-repeat;
            &:hover {
                -webkit-transition: 0.5s;
                transition: 0.5s;
                &:after {
                    position: absolute;
                    content: " ";
                    height: 100%;
                    width: 100%;
                    top: 0;
                    left: 0;
                    background: #7bf09885 !important;
                }
            }
            .card {
                background: transparent;
                padding-top: 112px;
                padding-bottom: 100px;
                border: none !important;
                margin-left: 65px;
                z-index: 3;
                @media screen and(max-width:768px) {
                    padding-top: 100px;
                }
                .card-title {
                    color: $color-white;
                    font-size: 5vw;
                    font-weight: bold;
                    padding-bottom: 20px;
                    text-transform: uppercase;
                    @media screen and(max-width:768px) {
                        font-size: 30px !important;
                    }
                }
                .card-text {
                    color: $color-white;
                    font-weight: 700;
                    font-size: 15px;
                }
            }
        }
        .main_right {
            background-size: cover;
            width: 100%;
            background-repeat: no-repeat;
            position: relative;
            &:hover {
                &:after {
                    position: absolute;
                    content: " ";
                    height: 100%;
                    width: 100%;
                    top: 0;
                    left: 0;
                    background: #7bf09885 !important;
                }
            }
            .card {
                background: transparent;
                padding-top: 112px;
                padding-bottom: 100px;
                border: none !important;
                margin-left: 65px;
                z-index: 3;
                @media screen and(max-width:768px) {
                    padding-top: 100px;
                    padding-bottom: 100px;
                }
                .card-title {
                    color: $color-white;
                    font-size: 5vw;
                    font-weight: bold;
                    padding-bottom: 20px;
                    text-transform: uppercase;
                    @media screen and(max-width:768px) {
                        font-size: 30px !important;
                    }
                }
                .card-text {
                    color: $color-white;
                    font-weight: 700;
                    font-size: 15px;
                }
            }
        }
    }
}
