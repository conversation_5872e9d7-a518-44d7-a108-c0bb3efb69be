.btn {
    border: none !important;
    outline: 0 !important;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    box-shadow: none !important;
    font-weight: 400;
    &-primary {
        background-color: transparent !important;
        border-color: $color-white;
        padding: 12px 36px;
        color: #fff !important;
        font-size: 16px;
        font-weight: 600;
        border-radius:0;
        border:3px solid !important;
        &:hover {
         
            border-color: $color-brand2 !important;
            color: $color-white !important;
            background: $color-brand2 !important;
        }
    }
    &:focus,
    &.focus {
      outline: 0;
      
    }

    &-cart {
        background-color:$color-brand !important;
        margin-right: 5px;
        padding: 2px 9px;
        color: #fff !important;
        font-size: 13px;
        border-radius: 0 !important;
     border: none;
        
    }

    &-checkout {
        background-color:$color-brand2 !important;
        padding: 2px 9px;
        color: #fff !important;
        font-size: 13px;
        border-radius: 0 !important;
         border: none;
    }
    &-details {
        background-color:transparent!important;
        padding: 2px 9px;
        color: #fff !important;
        font-size: 13px;
        border-radius: 0 !important;
        border: 1px solid !important;
        border-color:$color-white !important;
        border-radius: 50% !important;
    }
    &-checkout_m {
        background-color:$color-brand2 !important;
        text-decoration: none !important;
        padding: 7px 12px;
        color: #fff !important;
        font-size: 13px;
        border-radius: 0 !important;
     border: none;
      
    }
    &-cart_p {
        background-color:$color-brand2 !important;
        color: #fff !important;
        font-size: 13px;
        border-radius: 0 !important;
         border: none;  
padding: 13px 40px;
margin-left: 33px;
    }
    &-shopping {
        background-color:transparent !important;
        color: $color-brand !important;
        font-size: 20px;
        @media screen and(max-width:768px) {
            padding: 8px 15px;
           
            font-size: 2vw;
        }
        font-weight: 700;
        border-radius: 0 !important;
         border: 2px solid !important;
         border-color: $color-brand !important; 
padding: 13px 40px;
margin-left: 33px;
    }


    &-clear {
        background-color:transparent !important;
        color: $color-font !important;
      font-size: 20px;
        @media screen and(max-width:768px) {
            padding: 8px 15px;
            margin-bottom: 10px;
            font-size: 2vw;
        }
        font-weight: 700;
        border-radius: 0 !important;
        border: 2px solid !important;
        border-color: $color-border !important; 
        padding: 13px 40px;
        margin-left: 33px;
    }


    &-update {
        background-color:$color-h-bg !important;
        color: $color-font !important;
        font-size: 20px;
        @media screen and(max-width:768px) {
            padding: 8px 15px;
        
            font-size: 2vw;
        }
        font-weight: 700;
        border-radius: 0 !important;
        border: 2px solid !important;
        border-color: $color-h-bg !important; 
padding: 13px 40px;
margin-left: 33px;
    }

    &-checkout_c{
        text-transform: uppercase;
        background-color:$color-brand2 !important;
        text-decoration: none !important;
        width: 100%;
        height: 60px;
        color: #fff !important;
        font-size: 16px;
        font-weight: 700;
        border-radius: 0 !important;
     border: none;
     line-height: 50px;
height: 60px;
&:hover{
    background-color: $color-brand !important;
}
    }


    &-contact{
        margin-top: 30px;
        text-transform: uppercase;
        background-color:$color-brand2 !important;
        text-decoration: none !important;
        width: 100%;
        height: 60px;
        color: #fff !important;
        font-size: 16px;
        font-weight: 700;
        border-radius: 0 !important;
     border: none;
     line-height: 50px;
height: 60px;
&:hover{
    background-color: $color-brand !important;
}
    }
}