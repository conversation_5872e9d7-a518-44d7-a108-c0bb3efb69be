.amazing {
    margin-top: 20px;
    .amazing_bg {
        background-image: url(./../img/bg-img/bg-5.jpg);
        padding-top: 100px;
        background-size: cover;
        width: 100%;
        background-position: center top;
        background-size: cover;
        @media screen and(max-width: 768px) {
            padding-bottom: 50px;
            padding-top: 50px;
        }
        .amazing_content {
            margin: 100px 0;
            margin-left: auto;
            background-color: $color-transp;
            padding: 50px 50px;
            float: right;
            color: $color-white;
            @media screen and(max-width:768px) {
                float: none;
                margin: auto;
            }
        }
        h2 {
            font-size: 55px;
            font-weight: 600;
            margin-bottom: 20px;
            @media screen and(max-width:768px) {
                font-size: 30px;
            }
        }
        p {
            padding-bottom: 10px;
            font-size: 15px;
            font-weight: 700;
        }
        .rate {
            span {
                color: $color-white;
                font-size: 30px;
                font-weight: 500;
                text-decoration: line-through;
                &:last-child {
                    color: $color-brand2;
                    font-size: 35px;
                    margin-left: 10px;
                    font-weight: 600;
                    text-decoration: none;
                }
            }
            margin-bottom: 20px;
        }
    }
}
