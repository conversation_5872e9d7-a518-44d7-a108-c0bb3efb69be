# Tema Ryrox - Inspirado no Design da Localiza

## Visão Geral

Este documento descreve as modificações realizadas no tema Ryrox do Odoo para que a página inicial se assemelhe ao design do site da Localiza (empresa de aluguel de carros), mantendo o esquema de cores azuis característico do tema original.

## Alterações Realizadas

### 1. Hero Section Principal
- **Arquivo modificado**: `views/snippets/main_banner.xml`
- **Alterações**:
  - Substituição do carousel por uma hero section moderna
  - Implementação de formulário de busca centralizado
  - Layout responsivo com duas colunas
  - Seção de features com ícones

### 2. Header Moderno
- **Arquivo modificado**: `views/header_templates.xml`
- **Novo arquivo**: `static/src/scss/components/_header.scss`
- **Alterações**:
  - Header com informações de contato
  - Links para redes sociais
  - Design limpo e moderno
  - Call-to-action destacado

### 3. Seção de Serviços
- **Novo arquivo**: `views/snippets/services_section.xml`
- **Novo arquivo**: `static/src/scss/components/_services.scss`
- **Características**:
  - 6 cards de serviços com ícones
  - Efeitos hover interativos
  - Layout responsivo
  - Inspirado nos benefícios da Localiza

### 4. Seção de Produtos/Frota
- **Arquivo modificado**: `views/snippets/main_product.xml`
- **Arquivo modificado**: `static/src/scss/components/_product.scss`
- **Alterações**:
  - Layout de cards similar à frota da Localiza
  - Categorias de produtos (Premium, Econômica, Executiva)
  - Badges e tags de características
  - Preços destacados

### 5. Esquema de Cores Atualizado
- **Arquivo modificado**: `static/src/scss/_variables.scss`
- **Novo arquivo**: `static/src/scss/components/_localiza-theme.scss`
- **Cores principais**:
  - `$color-brand`: #1a365d (azul escuro)
  - `$color-brand2`: #2563eb (azul primário)
  - `$color-hover`: #f59e0b (amarelo/âmbar para destaques)

## Estrutura de Arquivos Criados/Modificados

```
addons/theme_ryrox/
├── views/
│   ├── header_templates.xml (modificado)
│   └── snippets/
│       ├── main_banner.xml (modificado)
│       ├── main_product.xml (modificado)
│       ├── services_section.xml (novo)
│       └── snippets_templates.xml (modificado)
├── static/src/scss/
│   ├── _variables.scss (modificado)
│   └── components/
│       ├── _banner.scss (modificado)
│       ├── _components.scss (modificado)
│       ├── _header.scss (novo)
│       ├── _localiza-theme.scss (novo)
│       ├── _product.scss (modificado)
│       └── _services.scss (novo)
└── __manifest__.py (modificado)
```

## Características do Design

### Inspiração Localiza
- Layout limpo e moderno
- Formulário de busca centralizado
- Seções de benefícios/serviços
- Cards de produtos/categorias
- Esquema de cores profissional

### Responsividade
- Design mobile-first
- Breakpoints otimizados
- Elementos adaptativos
- Navegação mobile aprimorada

### Interatividade
- Efeitos hover suaves
- Animações CSS
- Transições fluidas
- Feedback visual

## Como Usar

1. **Instalação**: O tema já está configurado no manifest
2. **Ativação**: Ative o tema no backend do Odoo
3. **Personalização**: Use o editor de website para ajustar conteúdos
4. **Snippets**: Arraste os novos snippets para suas páginas

## Snippets Disponíveis

- **Ryrox Hero Section**: Hero principal com formulário
- **Ryrox Services**: Seção de serviços/benefícios
- **Product Fleet**: Seção de produtos estilo frota

## Compatibilidade

- Odoo 18.0+
- Navegadores modernos
- Dispositivos móveis e desktop
- Funcionalidades do Odoo preservadas

## Manutenção

Para manter o tema atualizado:
1. Preserve as variáveis SCSS ao fazer alterações
2. Teste em diferentes dispositivos
3. Mantenha a consistência visual
4. Documente alterações personalizadas
