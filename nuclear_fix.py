#!/usr/bin/env python3
"""
Solução nuclear para o problema do QWeb - Remove completamente referências problemáticas
"""

import os
import shutil
import glob
import re

def backup_theme():
    """Cria backup do tema atual"""
    print("💾 Criando backup do tema...")
    
    backup_dir = 'theme_ryrox_backup'
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    try:
        shutil.copytree('addons/theme_ryrox', backup_dir)
        print(f"  ✅ Backup criado em: {backup_dir}")
        return True
    except Exception as e:
        print(f"  ❌ Erro ao criar backup: {e}")
        return False

def create_minimal_snippets():
    """Cria arquivo de snippets mínimo sem referências problemáticas"""
    print("🔧 Criando snippets mínimos...")
    
    minimal_snippets = '''<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="ryrox_minimal_snippets" inherit_id="website.snippets" name="Ryrox Minimal Snippets">
        <!-- Snippets mínimos para evitar conflitos -->
        <xpath expr="//snippets[@id='snippet_groups']" position="inside">
            <!-- Hero Section Localiza Style -->
            <t t-snippet="theme_ryrox.s_main" 
               t-thumbnail="/theme_xtream/static/src/img/snippets/main_banner.jpg"/>
            
            <!-- Services Section -->
            <t t-snippet="theme_ryrox.s_services" 
               t-thumbnail="/theme_xtream/static/src/img/snippets/services.jpg"/>
        </xpath>
        
        <xpath expr="//snippets[@id='snippet_content']//t[@t-snippet][last()]" position="after">
            <!-- Product Fleet -->
            <t t-snippet="theme_ryrox.s_main_product" 
               t-thumbnail="/theme_xtream/static/src/img/snippets/main_product.jpg"/>
        </xpath>
    </template>
</odoo>'''
    
    snippets_file = 'addons/theme_ryrox/views/snippets/snippets_templates.xml'
    
    try:
        with open(snippets_file, 'w', encoding='utf-8') as f:
            f.write(minimal_snippets)
        print(f"  ✅ Snippets mínimos criados: {snippets_file}")
        return True
    except Exception as e:
        print(f"  ❌ Erro ao criar snippets: {e}")
        return False

def remove_problematic_files():
    """Remove arquivos que podem estar causando conflito"""
    print("🗑️ Removendo arquivos problemáticos...")
    
    files_to_remove = [
        'addons/theme_ryrox/views/snippets/amazing.xml',
        'addons/theme_ryrox/views/snippets/discount.xml', 
        'addons/theme_ryrox/views/snippets/testimonial.xml',
        'addons/theme_ryrox/views/emergency_fix.xml'
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"  ✅ Removido: {file_path}")
            except Exception as e:
                print(f"  ❌ Erro ao remover {file_path}: {e}")
        else:
            print(f"  ℹ️  Não encontrado: {file_path}")

def update_manifest_minimal():
    """Atualiza manifest com apenas arquivos essenciais"""
    print("📋 Atualizando manifest...")
    
    manifest_content = '''# -*- coding: utf-8 -*-
{
    'name': 'Theme Ryrox Rental - Localiza Style',
    'version': '********.0',
    'category': 'Theme/eCommerce',
    'description': 'Design eCommerce Website with Theme Ryrox Rental - Localiza Inspired',
    'summary': 'Theme Ryrox Rental - Localiza Style',
    'author': 'Cybrosys Techno Solutions',
    'company': 'Cybrosys Techno Solutions',
    'maintainer': 'Cybrosys Techno Solutions',
    'website': "https://www.cybrosys.com",
    'depends': ['base', 'website_sale_wishlist', 'website_mass_mailing'],
    'data': [
        'security/ir.model.access.csv',
        'views/xtream_testimonials_views.xml',
        'views/contact_us_templates.xml',
        'views/footer_templates.xml',
        'views/shop_templates.xml',
        'views/header_templates.xml',
        'views/snippets/snippets_templates.xml',
        'views/snippets/new_arrivals.xml',
        'views/snippets/main_banner.xml',
        'views/snippets/main_product.xml',
        'views/snippets/services_section.xml',
    ],
    'assets': {
      'web.assets_frontend': [
          '/theme_xtream/static/src/css/animate.min.css',
          '/theme_xtream/static/src/css/owl.carousel.min.css',
          '/theme_xtream/static/src/css/owl.theme.default.min.css',
          '/theme_xtream/static/src/css/style.css',
          '/theme_xtream/static/src/js/owl.carousel.js',
          '/theme_xtream/static/src/js/owl.carousel.min.js',
          '/theme_xtream/static/src/js/new_arrivals.js',
          '/theme_xtream/static/src/js/testimonials.js',
          '/theme_xtream/static/src/js/custom.js',
      ]
    },
    'images': [
        'static/description/banner.jpg',
        'static/description/theme_screenshot.jpg',
    ],
    'license': 'LGPL-3',
    'installable': True,
    'application': False,
    'auto_install': False,
}'''
    
    manifest_file = 'addons/theme_ryrox/__manifest__.py'
    
    try:
        with open(manifest_file, 'w', encoding='utf-8') as f:
            f.write(manifest_content)
        print(f"  ✅ Manifest atualizado: {manifest_file}")
        return True
    except Exception as e:
        print(f"  ❌ Erro ao atualizar manifest: {e}")
        return False

def nuclear_cache_clean():
    """Limpeza nuclear de todos os caches"""
    print("💥 Limpeza NUCLEAR do cache...")
    
    # Remove todos os caches Python
    cache_patterns = [
        'addons/theme_ryrox/**/__pycache__',
        'addons/theme_ryrox/**/*.pyc',
        'addons/theme_ryrox/**/*.pyo'
    ]
    
    for pattern in cache_patterns:
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                else:
                    os.remove(file_path)
                print(f"  ✅ Removido: {file_path}")
            except Exception as e:
                print(f"  ❌ Erro ao remover {file_path}: {e}")

def show_nuclear_instructions():
    """Mostra instruções para aplicar a solução nuclear"""
    print("\n💥 SOLUÇÃO NUCLEAR APLICADA")
    print("=" * 50)
    print("🚨 INSTRUÇÕES CRÍTICAS:")
    print()
    print("1. PARE o servidor Odoo IMEDIATAMENTE (Ctrl+C)")
    print("2. Aguarde 30 segundos")
    print("3. Execute EXATAMENTE este comando:")
    print("   python odoo-bin -i theme_ryrox -d sua_database --dev=all")
    print()
    print("4. Se ainda der erro, execute:")
    print("   python odoo-bin -d sua_database --dev=all")
    print("   (sem instalar o tema)")
    print()
    print("5. No backend do Odoo:")
    print("   - Vá para Apps")
    print("   - Remova filtros")
    print("   - Procure 'Theme Ryrox'")
    print("   - Se estiver instalado, DESINSTALE")
    print("   - Reinstale o tema")
    print()
    print("🔄 ALTERNATIVA EXTREMA:")
    print("Se nada funcionar:")
    print("1. Renomeie a pasta theme_ryrox para theme_ryrox_old")
    print("2. Restaure o backup: mv theme_ryrox_backup theme_ryrox")
    print("3. Reinicie o Odoo")
    print()
    print("📞 SUPORTE:")
    print("Se o problema persistir, o cache pode estar no banco de dados.")
    print("Considere criar uma nova database para teste.")

if __name__ == "__main__":
    print("💥 SOLUÇÃO NUCLEAR - Tema Ryrox")
    print("=" * 40)
    print("⚠️  Esta é uma solução drástica!")
    print("⚠️  Certifique-se de ter backup!")
    print()
    
    if backup_theme():
        nuclear_cache_clean()
        remove_problematic_files()
        create_minimal_snippets()
        update_manifest_minimal()
        show_nuclear_instructions()
        
        print("\n💥 Solução nuclear aplicada!")
        print("🔄 Siga as instruções acima EXATAMENTE!")
    else:
        print("\n❌ Falha ao criar backup. Operação cancelada.")
        print("💡 Crie backup manual antes de continuar.")
