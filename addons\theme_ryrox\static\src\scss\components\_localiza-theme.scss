// Localiza-inspired theme enhancements
// Additional styles to ensure the Localiza look is properly implemented

// Global enhancements
body {
    font-family: $font-default;
    line-height: 1.6;
    color: $color-font2;
}

// Button enhancements
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &.btn-primary {
        background: $color-brand2;
        border-color: $color-brand2;
        
        &:hover {
            background: $color-brand-dark;
            border-color: $color-brand-dark;
            transform: translateY(-1px);
        }
    }
    
    &.btn-outline-primary {
        color: $color-brand2;
        border-color: $color-brand2;
        
        &:hover {
            background: $color-brand2;
            border-color: $color-brand2;
        }
    }
}

// Form enhancements
.form-control {
    border-radius: 8px;
    border: 2px solid $color-border;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    
    &:focus {
        border-color: $color-brand2;
        box-shadow: 0 0 0 0.2rem rgba($color-brand2, 0.25);
    }
}

// Card enhancements
.card {
    border-radius: 15px;
    border: 1px solid $color-border;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }
}

// Section spacing
section {
    padding: 4rem 0;
    
    @media (max-width: 768px) {
        padding: 2rem 0;
    }
}

// Typography enhancements
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: $color-brand;
    line-height: 1.3;
}

h1 {
    font-size: 3rem;
    
    @media (max-width: 768px) {
        font-size: 2.5rem;
    }
}

h2 {
    font-size: 2.5rem;
    
    @media (max-width: 768px) {
        font-size: 2rem;
    }
}

h3 {
    font-size: 2rem;
    
    @media (max-width: 768px) {
        font-size: 1.75rem;
    }
}

// Link enhancements
a {
    color: $color-brand2;
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
        color: $color-brand-dark;
    }
}

// Utility classes
.text-primary {
    color: $color-brand2 !important;
}

.bg-primary {
    background-color: $color-brand2 !important;
}

.border-primary {
    border-color: $color-brand2 !important;
}

// Animation utilities
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

// Responsive utilities
@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

// Print styles
@media print {
    .ryrox_header_info,
    .ryrox_header_cta,
    .navbar,
    footer {
        display: none !important;
    }
}
