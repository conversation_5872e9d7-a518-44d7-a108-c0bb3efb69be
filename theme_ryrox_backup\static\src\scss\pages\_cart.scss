.cart {
    margin-top: 90px;
    .table_wrapper {
        .table-responsive {
            overflow-x: auto;
        }
        .table {
            overflow-x: auto;
            thead {
                background-color: $color-h-bg;
            }
            tbody {
                .cart_img {
                    display: flex;
                    align-items: center;
                    .wrapper {
                        max-width: 150px;
                        img {
                            width: 100%;
                        }
                    }
                    h6 {
                        color: $color-brand;
                        font-size: 23px;
                        padding-left: 20px;
                        @media screen and(max-width:600px) {
                            font-size: 14px;
                            padding-left: 10px;
                        }
                    }
                }
                td {
                    vertical-align: middle;
                }
                .cart_q {
                    #myform {
                        text-align: center;
                        padding: 5px;
                        border: 1px solid #ccc;
                        display: flex;
                        margin: 2%;
                        width: 95px;
                    }
                    .qty {
                        width: 40px;
                        height: 25px;
                        text-align: center;
                        border: none;
                    }
                    input.qtyplus {
                        width: 25px;
                        height: 25px;
                        background-color: transparent;
                        border: none;
                    }
                    input.qtyminus {
                        width: 25px;
                        height: 25px;
                        background-color: transparent;
                        border: none;
                    }
                }
            }
        }
    }
    .c_buttons {
        margin-top: 30px;
    }
    .cart_bottom {
        margin-top: 70px;
        .coupen {
            padding-top: 30px;
            .hb {
                h5 {
                    color: $color-brand;
                    font-size: 23px;
                    font-weight: 700;
                }
                p {
                    color: $color-font;
                }
            }
            .input-group {
                width: 100%;
                height: 50px;
                border-radius: 0;
                margin-top: 70px;
                .form-control {
                    border-radius: 0;
                    height: 50px;
                }
                .input-group-text {
                    border-radius: 0;
                    background-color: $color-brand2;
                    padding: 0 20px;
                    color: $color-white;
                    border: 1px solid;
                    font-weight: 700;
                }
            }
            .form-control:focus {
                color: #495057;
                background-color: #fff;
                border-color: $color-brand2;
                outline: 0;
                box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
            }
            .radio_wrapper {
                margin-top: 35px;
            }
            .custom-control {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
                margin-right: 20px;
                p {
                    color: $color-black;
                }
            }
            .cart-total-chart {
                padding-left: 0;
                margin-top: 35px;
                li {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 30px;
                    margin-right: 20px;
                }
            }
        }
    }
}
