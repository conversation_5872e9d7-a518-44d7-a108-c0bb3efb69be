// Ryrox Hero Section - Localiza inspired
.ryrox_hero_section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;

    .hero_background {
        background: linear-gradient(135deg, $color-brand2 0%, lighten($color-brand2, 10%) 100%);
        width: 100%;
        min-height: 100vh;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/theme_xtream/static/src/img/bg-img/bg-1.jpg') center/cover;
            opacity: 0.1;
            z-index: 1;
        }

        .container {
            position: relative;
            z-index: 2;
        }
    }

    .hero_content {
        padding: 2rem 0;

        .hero_title {
            font-size: 3.5rem;
            font-weight: 700;
            color: $color-white;
            margin-bottom: 1.5rem;
            line-height: 1.2;

            @media (max-width: 768px) {
                font-size: 2.5rem;
            }
        }

        .hero_subtitle {
            font-size: 1.2rem;
            color: rgba($color-white, 0.9);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero_features {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;

            @media (max-width: 576px) {
                gap: 1rem;
            }

            .feature_item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: $color-white;
                font-weight: 500;

                i {
                    font-size: 1.2rem;
                    color: $color-hover;
                }
            }
        }
    }

    .hero_form_container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2rem 0;

        @media (max-width: 991px) {
            margin-top: 2rem;
        }
    }

    .search_form_card {
        background: $color-white;
        border-radius: 15px;
        padding: 2.5rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;

        .form_title {
            font-size: 1.5rem;
            font-weight: 600;
            color: $color-brand;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .hero_search_form {
            .form_group {
                margin-bottom: 1.5rem;

                label {
                    display: block;
                    font-weight: 500;
                    color: $color-brand;
                    margin-bottom: 0.5rem;
                    font-size: 0.9rem;
                }

                .form-control {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 0.75rem 1rem;
                    font-size: 1rem;
                    transition: all 0.3s ease;

                    &:focus {
                        border-color: $color-brand2;
                        box-shadow: 0 0 0 0.2rem rgba($color-brand2, 0.25);
                        outline: none;
                    }
                }

                select.form-control {
                    cursor: pointer;
                }
            }

            .btn-search {
                width: 100%;
                padding: 1rem;
                font-size: 1.1rem;
                font-weight: 600;
                border-radius: 8px;
                background: $color-brand2;
                border: none;
                color: $color-white;
                transition: all 0.3s ease;

                &:hover {
                    background: darken($color-brand2, 10%);
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba($color-brand2, 0.4);
                }

                i {
                    margin-right: 0.5rem;
                }
            }
        }
    }
}

.banner_main{

    .banner_bg2{
        background-image: linear-gradient(#11111191, #11111191),url(./../img/bg-img/bg-2.jpg);
        justify-content: center;
      
        height: 100vh;
         background-size: cover;
         width: 100%;
         background-repeat: no-repeat;

         @media screen and(max-width:768px) {
            height: 70vh;
           
        }


        

         .card{
            background: transparent;
            padding-top: 175px;
            padding-bottom: 100px;
            border: none !important;

            
            @media screen and(max-width:600px) {
                padding-left: 40px;
              }
            

            @media screen and(max-width:768px) {
                padding-top:100px;
                padding-bottom: 0;
            }
            .card-title{
                color:$color-white;
                font-size:7vw;
                font-weight: bold;
                padding-bottom: 20px;
              
            }
            .card-text{
                color:$color-white;
             font-weight: 700;
                font-size:15px;
              
              
            }
        }
    
    }
    
    .banner_bg3{
    
        background-image: linear-gradient(#11111191, #11111191),url(./../img/bg-img/bg-4.jpg);
        justify-content: center;
      
        height: 100vh;
         background-size: cover;
         width: 100%;
         background-repeat: no-repeat;


         @media screen and(max-width:768px) {
            height: 70vh;
        }

         .card{
            background: transparent;
            padding-top: 175px;
            padding-bottom: 100px;
            border: none !important;
            animation-name: fadeInOut;
            animation-delay: 1s;
            animation-duration: 3s;

            
            @media screen and(max-width:600px) {
                padding-left: 40px;
              }

            @media screen and(max-width:768px) {
                padding-top:50px;
                padding-bottom: 100px;
            }
            .card-title{
                color:$color-white;
                font-size:5vw;
                font-weight: bold;
                padding-bottom: 20px;
                text-transform: uppercase;
              
            }
            .card-text{
                color:$color-white;
                padding-bottom: 30px;
                font-size: 14px;
              
            }
        }

      .breadcrumb{
          background: transparent;
          padding-top: 110px;
          padding-bottom: 110px;

          .breadcrumb-item{
              color: $color-hover;

              &:first-child{
                  &::before{
                      display: none;
                  }
              }

              &::before{
                
                    display: inline-block;
                    padding-right: 1.5rem;
                    color: #fff;
                    content: "/";
                
              }
          
              a{
                  color: $color-white;
                  text-decoration: none;
                  &:hover{
                    color:$color-hover;
                  }
              }
          }
      }
    
    }

    .banner_bg4{
        background-image: url(./../images/banner/home.jpg);
        justify-content: center;
        max-width: 1400px;
        margin: auto;
   
    background-size: cover;
    width: 100%;
    background-repeat: no-repeat;
    background-position: center;
        margin-top: 134px;
        

      .breadcrumb{
          background: transparent;
          padding-top: 110px;
          padding-bottom: 110px;

          .breadcrumb-item{
              color: $color-hover;

              &:first-child{
                  &::before{
                      display: none;
                  }
              }

              &::before{
                
                    display: inline-block;
                    padding-right: 1.5rem;
                    color: #fff;
                    content: "/";
                
              }
          
              a{
                  color: $color-white;
                  text-decoration: none;
                  &:hover{
                    color:$color-hover;
                  }
              }
          }
      }
    
    }


    
.owl-carousel button.owl-dot span {
    height: 10px;
    width: 10px;
    color: $color-white;
    background-color: $color-white;
    border-radius: 50%;
    display: block;
    font-weight: 700;
    margin: 5px;
}
.owl-carousel button.owl-dot.active span{
   background-color: $color-brand2;
}


.owl-carousel{

    .owl-dots{
            position: absolute;
    bottom:250px;
    left: 40px;
    transform: rotate(89deg);
    background-color: transparent;

    @media screen and(max-width:1150px){
            left: 0 !important;
        
    }
    @media screen and(max-width:768px) {
       
    }
    @media screen and(max-width:600px) {
       
     }
}
}

}


.slideInDown {
    -webkit-animation-name: slideInDown;
    animation-name: slideInDown;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    }
    @-webkit-keyframes slideInDown {
    0% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    visibility: visible;
    }
    100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    }
    }
    @keyframes slideInDown {
    0% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    visibility: visible;
    }
    100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    }
    } 



    .tada {
        -webkit-animation-name: tada;
        animation-name: tada;
        -webkit-animation-duration: 1s;
        animation-duration: 1s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
        }
        @-webkit-keyframes tada {
        0% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
        }
        10%, 20% {
        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        }
        30%, 50%, 70%, 90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        }
        40%, 60%, 80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        }
        100% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
        }
        }
        @keyframes tada {
        0% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
        }
        10%, 20% {
        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        }
        30%, 50%, 70%, 90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        }
        40%, 60%, 80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        }
        100% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
        }
        } 

        .slideInUp {
            -webkit-animation-name: slideInUp;
            animation-name: slideInUp;
            -webkit-animation-duration: 1s;
            animation-duration: 1s;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            }
            @-webkit-keyframes slideInUp {
            0% {
            -webkit-transform: translateY(100%);
            transform: translateY(100%);
            visibility: visible;
            }
            100% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
            }
            }
            @keyframes slideInUp {
            0% {
            -webkit-transform: translateY(100%);
            transform: translateY(100%);
            visibility: visible;
            }
            100% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
            }
            } 
          
         
            .slideInLeft {
                -webkit-animation-name: slideInLeft;
                animation-name: slideInLeft;
                -webkit-animation-duration: 1s;
                animation-duration: 1s;
                -webkit-animation-fill-mode: both;
                animation-fill-mode: both;
                }
                @-webkit-keyframes slideInLeft {
                0% {
                -webkit-transform: translateX(-100%);
                transform: translateX(-100%);
                visibility: visible;
                }
                100% {
                -webkit-transform: translateX(0);
                transform: translateX(0);
                }
                }
                @keyframes slideInLeft {
                0% {
                -webkit-transform: translateX(-100%);
                transform: translateX(-100%);
                visibility: visible;
                }
                100% {
                -webkit-transform: translateX(0);
                transform: translateX(0);
                }
                } 


                .fadeInDownBig {
                    -webkit-animation-name: fadeInDownBig;
                    animation-name: fadeInDownBig;
                    -webkit-animation-duration: 1s;
                    animation-duration: 1s;
                    -webkit-animation-fill-mode: both;
                    animation-fill-mode: both;
                    }
                    @-webkit-keyframes fadeInDownBig {
                    0% {
                    opacity: 0;
                    -webkit-transform: translate3d(0, -2000px, 0);
                    transform: translate3d(0, -2000px, 0);
                    }
                    100% {
                    opacity: 1;
                    -webkit-transform: none;
                    transform: none;
                    }
                    }
                    @keyframes fadeInDownBig {
                    0% {
                    opacity: 0;
                    -webkit-transform: translate3d(0, -2000px, 0);
                    transform: translate3d(0, -2000px, 0);
                    }
                    100% {
                    opacity: 1;
                    -webkit-transform: none;
                    transform: none;
                    }
                    } 
                  
                  
                    

                    .fadeInLeftBig {
               -webkit-animation-name: fadeInLeftBig;
               animation-name: fadeInLeftBig;
               -webkit-animation-duration: 1s;
               animation-duration: 1s;
               -webkit-animation-fill-mode: both;
               animation-fill-mode: both;
               }
               @-webkit-keyframes fadeInLeftBig {
               0% {
               opacity: 0;
               -webkit-transform: translate3d(-2000px, 0, 0);
               transform: translate3d(-2000px, 0, 0);
               }
               100% {
               opacity: 1;
               -webkit-transform: none;
               transform: none;
               }
               }
               @keyframes fadeInLeftBig {
               0% {
               opacity: 0;
               -webkit-transform: translate3d(-2000px, 0, 0);
               transform: translate3d(-2000px, 0, 0);
               }
               100% {
               opacity: 1;
               -webkit-transform: none;
               transform: none;
               }
               } 
             