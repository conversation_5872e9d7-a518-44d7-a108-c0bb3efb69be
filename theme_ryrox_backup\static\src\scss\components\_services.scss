// Services Section Styles - Localiza inspired
.ryrox_services_section {
    background: #f8f9fa;
    padding: 5rem 0;
    
    .services_title {
        font-size: 2.5rem;
        font-weight: 700;
        color: $color-brand;
        margin-bottom: 1rem;
        
        @media (max-width: 768px) {
            font-size: 2rem;
        }
    }
    
    .services_subtitle {
        font-size: 1.2rem;
        color: $color-font;
        margin-bottom: 0;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .service_card {
        background: $color-white;
        border-radius: 15px;
        padding: 2.5rem 2rem;
        text-align: center;
        height: 100%;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
        position: relative;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba($color-brand2, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        &:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-color: $color-brand2;
            
            &::before {
                left: 100%;
            }
            
            .service_icon {
                transform: scale(1.1);
                background: $color-brand2;
                
                i {
                    color: $color-white;
                }
            }
            
            .service_link {
                color: $color-brand2;
                text-decoration: underline;
            }
        }
        
        .service_icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba($color-brand2, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
            
            i {
                font-size: 2rem;
                color: $color-brand2;
                transition: all 0.3s ease;
            }
        }
        
        .service_title {
            font-size: 1.3rem;
            font-weight: 600;
            color: $color-brand;
            margin-bottom: 1rem;
        }
        
        .service_description {
            color: $color-font;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }
        
        .service_link {
            color: $color-brand2;
            font-weight: 500;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            
            &:hover {
                color: darken($color-brand2, 10%);
            }
        }
    }
    
    @media (max-width: 768px) {
        padding: 3rem 0;
        
        .service_card {
            padding: 2rem 1.5rem;
            margin-bottom: 1rem;
        }
    }
}
