.choose {
  margin: 90px 0;
  .wrapper {
    padding: 10px 0;
  }
  .choose_left {
    color: $color-grey;
    font-size: $font-text;
  }
  .choose_right {
    @media screen and(max-width:768px) {
      float: none;
    }
    float: right;
    /* Custom dropdown */
    .custom-dropdown {
      width: 300px;
      border: 1px solid;
      border-color: $color-border;
      border-radius: 5px;
      position: relative;
      display: inline-block;
      vertical-align: middle;
      font-size: $font-text;
      -webkit-transition: 0.5s;
      transition: 0.5s;
    }
    .custom-dropdown select {
      width: 300px;
      background-color: #ffffff;
      color: $color-grey;
      font-size: inherit;
      padding: 0.7em;
      padding-right: 2.5em;
      margin: 0;
      border: none !important;
      text-indent: 0.01px;
      text-overflow: "";
      /*Hiding the select arrow for firefox*/
      -moz-appearance: none;
      /*Hiding the select arrow for chrome*/
      -webkit-appearance: none;
      /*Hiding the select arrow default implementation*/
      appearance: none;
    }
    /*Hiding the select arrow for IE10*/
    .custom-dropdown select::-ms-expand {
      display: none;
    }
    .custom-dropdown::before,
    .custom-dropdown::after {
      content: "";
      position: absolute;
      pointer-events: none;
    }
    .custom-dropdown::after {
      /*  Custom dropdown arrow */
      content: "\25BC";
      height: 1em;
      font-size: 0.625em;
      line-height: 1;
      right: 1.2em;
      top: 50%;
      margin-top: -0.5em;
    }
    .custom-dropdown::before {
      /*  Custom dropdown arrow cover */
      width: 2em;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 0 3px 3px 0;
      background-color: rgba(0, 0, 0, 0.2);
    }
    .custom-dropdown::after {
      color: rgba(0, 0, 0, 0.6);
    }
    .custom-dropdown select[disabled] {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
