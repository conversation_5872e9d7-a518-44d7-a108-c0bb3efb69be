.testiomonial {
    margin-top: 90px;
    .wrapper {
        position: relative;
        h2 {
            text-align: center;
            font-size: 60px;
            font-weight: 700;
            color: $color-brand;
            text-transform: uppercase;
            padding-bottom: 30px;
            @media screen and(max-width:768px) {
                font-size: 30px;
            }
        }
        .testimonial_content {
            padding: 0px 111px;
            padding-top: 30px;
            @media screen and(max-width:992px) {
                padding-left: 0;
                padding-right: 0;
            }
            span {
                color: $color-brand2;
                font-size: 30px;
                text-align: center;
                justify-content: center;
                display: flex;
            }
            .pp {
                margin: 40px 0;
                font-size: 17px;
                line-height: 2;
                font-weight: 700;
                color: $color-font;
                text-align: center;
                padding: 0 100px;
                @media screen and(max-width:768px) {
                    padding: 0 50px;
                }
            }
            .img_test {
                padding-top: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                .wrapper {
                    max-width: 75px;
                    img {
                        width: 100%;
                        border-radius: 50%;
                    }
                }
                .name {
                    margin-left: 20px;
                    p {
                        color: $color-brand;
                        font-size: 18px;
                        font-weight: 700;
                        margin: 0;
                    }
                    span {
                        color: $color-brand2;
                        font-size: 16px;
                        font-weight: 700;
                    }
                }
            }
        }
    }
    .owl-carousel button.owl-dot span {
        height: 10px;
        width: 10px;
        border: 1px solid !important;
        border-color: $color-font !important;
        color: $color-white;
        // background-color: $color-brand2 !important;
        border-radius: 50%;
        display: block;
        font-weight: 700;
        margin: 5px;
    }
    .owl-carousel button.owl-dot.active span {
        background-color: $color-brand2 !important;
    }
    .owl-carousel {
        .owl-dots {
            position: absolute;
            bottom: 165px;
            left: 0px !important;
            transform: rotate(90deg);
            background-color: transparent;
            @media screen and(max-width:1000px) {
                left: 150px;
            }
            @media screen and(max-width:768px) {
                left: 100px;
            }
            @media screen and(max-width:600px) {
                left: 75px;
            }
        }
    }
}
