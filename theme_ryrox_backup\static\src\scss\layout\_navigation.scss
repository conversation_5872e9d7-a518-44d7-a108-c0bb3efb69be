.top_nav {
    margin: 25px 0;
    .wrapper {
        display: flex;
        justify-content: space-between;
        padding-top: 20px;
        align-items: center;
        @media screen and(max-width:572px) {
            display: block;
        }
        .nav_center {
            margin: auto;
            @media screen and(max-width:992px) {
                margin: 0;
            }
            a {
                text-decoration: none;
            }
            .heading {
                color: $color-brand;
                font-size: 30px;
                font-weight: 700;
                @media screen and(max-width:572px) {
                    text-align: center;
                }
                @media screen and(max-width:768px) {
                    font-size: 3vw;
                }
                letter-spacing: 12px;
                &::first-letter {
                    color: $color-brand2;
                }
                hr {
                    background: $color-font;
                    margin: 3px 7px;
                    padding-right: 5px;
                }
                p {
                    color: $color-font;
                    font-weight: normal;
                    text-align: center;
                    @media screen and(max-width:572px) {
                        font-size: 12px;
                    }
                }
            }
        }
        .nav_right {
            display: flex;
            align-items: center;
            @media screen and(max-width:572px) {
                justify-content: center;
            }
            .bag {
                position: relative;
                span {
                    color: $color-white;
                    background-color: $color-brand2;
                    height: 20px;
                    width: 20px;
                    font-size: 13px;
                    position: absolute;
                    left: -8px;
                    top: 12px;
                    border-radius: 50%;
                }
            }
            .dropdown {
                i {
                    font-size: 20px;
                    padding-right: 7px;
                }
                .d_image {
                    display: block;
                    max-width: 40px;
                    img {
                        width: 100%;
                    }
                }
                .dropdown-menu {
                    padding: 30px 20px;
                    .nav_product {
                        color: $color-black;
                        margin-top: 10px;
                        span {
                            color: $color-brand;
                        }
                    }
                    .drop_buttons {
                        margin-top: 10px;
                        span {
                            padding-left: 5px;
                        }
                    }
                }
            }
            .side_b {
                background-color: $color-brand;
                color: $color-white;
                border-radius: 50%;
                padding-top: 5px;
                font-size: 30px;
                cursor: pointer;
                height: 55px;
                display: block;
                width: 55px;
                padding-left: 14px;
                @media screen and(max-width:768px) {
                    background-color: $color-brand;
                    color: $color-white;
                    border-radius: 50%;
                    padding-top: 6px !important;
                    font-size: 15px !important;
                    cursor: pointer;
                    height: 35px;
                    display: block;
                    width: 35px;
                    padding-left: 11px !important;
                }
            }
        }
    }
}
.navigation {
    padding: 35px 0 50px 0px;
    .help-line {
        background-color: $color-brand2;
        width: auto;
        height: 35px;
        padding: 0 30px;
        display: block;
        line-height: 35px;
        font-size: 14px;
        font-weight: 600;
        a {
            color: $color-white;
            text-decoration: none;
        }
        &:hover {
            background-color: $color-brand;
        }
    }
}
.navbar-dark {
    padding: 20px 0;
    @media screen and(max-width:768px) {
        display: grid;
        grid-template-columns: 1fr;
        grid-row-gap: 30px;
    }
    .navbar-toggler {
        border: 2px solid;
        border-color: $color-brand2;
        background-color: $color-brand2;
        border-radius: 0;
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='white' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
    }
    .navbar-brand {
        @media screen and(max-width:768px) {
            margin: auto;
        }
        .footer_icon {
            position: relative;
            .link_top {
                color: $color-white;
                background: $color-brand2;
                font-size: 11px;
                padding: 5px 12px;
                font-weight: 700;
                position: absolute;
                top: -43px;
                left: 9px;
                &:after {
                    position: absolute;
                    z-index: 2;
                    content: "";
                    width: 0;
                    height: 0;
                    border-style: solid;
                    border-width: 0 9px 9px 9px;
                    border-color: transparent transparent #7bf098 transparent;
                    bottom: -2px;
                    left: -6px;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                }
            }
        }
        a {
            color: $color-black;
            margin-right: 15px;
            &:hover {
                color: $color-font;
            }
        }
    }
}
.navbar-nav {
    text-align: center;
    position: relative;
    .link_top {
        color: $color-white;
        background: $color-brand2;
        font-size: 11px;
        padding: 5px 15px;
        font-weight: 700;
        position: absolute;
        top: -29px;
        left: 221px;
        &:after {
            position: absolute;
            z-index: 2;
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 9px 9px 9px;
            border-color: transparent transparent #7bf098 transparent;
            bottom: -2px;
            left: -6px;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
            // bottom: -1px;
            // left: -6px;
            // transform: rotate(-36deg);
        }
    }
    .nav-item {
        .dropdown-menu {
            @media screen and(max-width:992px) {
                text-align: center;
            }
            .dropdown-item {
                padding: 10px 10px;
                &:hover {
                    color: $color-brand;
                }
            }
        }
        .nav-link {
            color: $color-black;
            padding-right: 15px;
            padding-left: 15px;
            position: relative;
            z-index: 1;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            @media screen and(max-width:992px) {
                padding: 10px 0;
            }
            
        }
    }
}
.navbar-nav .nav-item.active .nav-link {
    color: $color-brand !important;
}
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
    color: $color-font !important;
}
/* ============ desktop view ============ */
@media all and (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: none;
    }
    .navbar .nav-item:hover .nav-link {
        color: #181818;
    }
    .navbar .nav-item:hover .dropdown-menu {
        display: block;
        border: none;
    }
    .navbar .nav-item .dropdown-menu {
        margin-top: 0;
        padding: 20px 10px;
    }
}
/* ============ desktop view .end// ============ */
#arrow {
    position: relative;
    top: 0;
    transition: top ease 0.5s;
}
#arrow:hover {
    top: -5px;
}
.navbar-dark .navbar-nav .nav-item .dropdown-menu .dropdown-item:active {
    background-color: $color-brand2;
    color: $color-white;
}
.sidenav {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 4;
    top: 0;
    left: 0;
    background-color: $color-white;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
}
.sidenav a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: $color-brand;
    display: block;
    transition: 0.3s;
}
.sidenav a:hover {
    color: $color-brand2;
}
.sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}
#main {
    transition: margin-left 0.5s;
    padding: 16px;
}
@media screen and (max-height: 450px) {
    .sidenav {
        padding-top: 15px;
    }
    .sidenav a {
        font-size: 18px;
    }
}
