.contact {
    margin-top: 90px;
    margin-bottom: 90px;
    .contact_left {
        .name {
            h3 {
                font-weight: 700;
                color: $color-brand;
                text-transform: uppercase;
            }
            p {
                color: $color-grey;
            }
            .contact-form {
                margin-top: 70px;
                .form-control {
                    display: block;
                    width: 100%;
                    height: calc(2.5em + 0.75rem + 2px);
                    padding: 0.375rem 0.75rem;
                    font-size: 1rem;
                    font-weight: 400;
                    line-height: 1.5;
                    color: #495057;
                    background-color: #fff;
                    background-clip: padding-box;
                    border: 1px solid #ced4da;
                    border-color: transparent;
                    border-bottom-color: $color-border;
                    border-radius: 0;
                    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                }
                .form-control:focus {
                    color: #495057;
                    background-color: #fff;
                    border-bottom-color: $color-brand2 !important;
                    outline: 0;
                    box-shadow: none;
                }
                .input-block {
                    margin-bottom: 30px;
                    label {
                        color: $color-grey;
                    }
                }
            }
        }
    }
    .contact_right {
        @media screen and(max-width:992px) {
            margin-top: 50px;
        }
        .c_info {
            margin-bottom: 60px;
        }
        h3 {
            margin-bottom: 30px;
            text-transform: uppercase;
            font-weight: 700;
            color: $color-brand;
        }
        .phone {
            padding-top: 30px;
            span {
                padding-right: 20px;
            }
            a {
                color: $color-grey;
                text-decoration: none;
                &:hover {
                    color: $color-hover !important;
                }
            }
        }
    }
}
.map {
    .mapouter {
        position: relative;
        text-align: right;
        height: 500px;
        widows: 100%;
        .mapouter {
            overflow: hidden;
            background: none !important;
            height: 100%;
            width: 100%;
        }
    }
}
