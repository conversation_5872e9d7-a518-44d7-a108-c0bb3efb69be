<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Hero Section inspired by Localiza -->
    <template id="s_main" name="Ryrox Hero Section">
        <section class="ryrox_hero_section" data-vxml="001">
            <div class="hero_background">
                <div class="container">
                    <div class="row align-items-center min-vh-100">
                        <div class="col-lg-6">
                            <div class="hero_content">
                                <h1 class="hero_title">Encontre o que você precisa</h1>
                                <p class="hero_subtitle">A melhor experiência de compra online com produtos de qualidade e entrega rápida.</p>
                                <div class="hero_features">
                                    <div class="feature_item">
                                        <i class="fa fa-truck"></i>
                                        <span>Entrega Rápida</span>
                                    </div>
                                    <div class="feature_item">
                                        <i class="fa fa-shield"></i>
                                        <span>Compra Segura</span>
                                    </div>
                                    <div class="feature_item">
                                        <i class="fa fa-star"></i>
                                        <span>Qualidade Garantida</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="hero_form_container">
                                <div class="search_form_card">
                                    <h3 class="form_title">Faça sua busca</h3>
                                    <form class="hero_search_form" action="/shop" method="get">
                                        <div class="form_group">
                                            <label for="search_category">Categoria</label>
                                            <select name="category" id="search_category" class="form-control">
                                                <option value="">Todas as categorias</option>
                                                <option value="electronics">Eletrônicos</option>
                                                <option value="fashion">Moda</option>
                                                <option value="home">Casa e Jardim</option>
                                                <option value="sports">Esportes</option>
                                            </select>
                                        </div>
                                        <div class="form_group">
                                            <label for="search_term">O que você procura?</label>
                                            <input type="text" name="search" id="search_term" class="form-control" placeholder="Digite o produto desejado"/>
                                        </div>
                                        <div class="form_group">
                                            <label for="price_range">Faixa de preço</label>
                                            <select name="price_range" id="price_range" class="form-control">
                                                <option value="">Qualquer preço</option>
                                                <option value="0-50">Até R$ 50</option>
                                                <option value="50-100">R$ 50 - R$ 100</option>
                                                <option value="100-200">R$ 100 - R$ 200</option>
                                                <option value="200+">Acima de R$ 200</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary btn-search">
                                            <i class="fa fa-search"></i>
                                            Buscar Produtos
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>
</odoo>
