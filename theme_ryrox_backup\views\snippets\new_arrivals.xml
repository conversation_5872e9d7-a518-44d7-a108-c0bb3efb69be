<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Startup template -->
    <template id="s_new_arrivals" name="New Arrivals">
        <section class="arrivals">
            <div class="container o_not_editable">
                <div class="css_non_editable_mode_hidden">
                    <div class="alert alert-info">
                        <h4>Snippet will be displayed here... Please Save to view the snippet.</h4>
                    </div>
                </div>
            </div>
        </section>
    </template>
    <!-- Dynamic template for new arrivals -->
    <template id="new_arrivals_dynamic" name="New Arrivals">
        <section class="body_section">
            <section class="product">
                <div class="container">
                    <div class="main">
                        <h2>New Arrivals</h2>
                    </div>
                    <div class="row">
                        <t t-foreach="product_ids" t-as="product_id">
                            <div class=" col-12 col-sm-6 col-md-4 product_column">
                                <a t-attf-href="/shop/product/#{slug(product_id)}">
                                    <div class="content">
                                        <div class="img_zoom">
                                            <img  id="zoomIn" t-attf-src="/web/image?model=product.template&amp;field=image_1920&amp;id=#{product_id.id}" class="img img-responsive img-fluid"/>
                                        </div>
                                        <p>
                                            <span t-if="product_id"
                                                  t-esc="product_id.list_price"
                                                  t-options="{'widget':'monetary', 'display_currency':product_id.currency_id}"/>
                                        </p>
                                        <h6>
                                            <span t-if="product_id" t-esc="product_id.display_name"/>
                                        </h6>
                                        <a t-attf-href="/shop/#{slug(product_id)}" class="">View Product</a>
                                    </div>
                                </a>
                            </div>
                        </t>
                    </div>
                </div>
            </section>
        </section>
    </template>
</odoo>
