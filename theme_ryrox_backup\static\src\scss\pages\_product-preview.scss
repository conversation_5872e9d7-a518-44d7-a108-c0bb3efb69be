.product_preview {
    margin-top: 90px;
    .breadcrumb {
        background: transparent;
        margin-top: 40px;
        padding-left: 0;
        .breadcrumb-item {
            a {
                color: $color-brand;
                text-decoration: none;
            }
        }
    }
    .back_to_page {
        a {
            font-size: 12px;
            color: $color-brand;
            text-decoration: none;
        }
        span {
            margin-right: 5px;
            font-size: 11px;
        }
    }
    .product_p {
        margin-top: 90px;
        .wrapper {
            position: relative;
            .preview_details {
                @media screen and(max-width:768px) {
                    margin-top: 70px;
                }
                h5 {
                    color: $color-black;
                    font-size: 25px;
                }
                .price {
                    font-size: 25px;
                    font-weight: 700;
                    margin-top: 10px;
                    color: $color-brand;
                }
                .stock {
                    font-size: 12px;
                    line-height: 1.5;
                    span {
                        font-size: 12px;
                        line-height: 1.5;
                        color: $color-font;
                    }
                }
                .rating {
                    display: flex;
                    padding-left: 0;
                    padding-top: 10px;
                    li {
                        a {
                            margin-right: 4px;
                            color: #ff9800;
                            span {
                                font-size: 13px;
                            }
                        }
                    }
                }
                .size_wrapper {
                    h4 {
                        color: $color-brand;
                        font-size: 14px;
                        font-weight: 700;
                        padding-top: 15px;
                    }
                    .size {
                        display: flex;
                        padding-left: 0;
                        a {
                            text-decoration: none;
                        }
                        span {
                            font-size: 14px;
                            color: $color-black;
                            display: block;
                            padding: 8px 12px;
                            font-size: 13px;
                            background-color: transparent;
                            font-weight: 700;
                            margin-right: 12px;
                            border: 2px solid;
                            border-color: $color-brand;
                            &:hover {
                                background-color: $color-brand2 !important;
                                color: $color-white;
                                border-color: $color-brand2 !important ;
                            }
                        }
                    }
                }
                .product_quantity {
                    display: flex;
                    margin-top: 45px;
                    #myform {
                        text-align: center;
                        border: 2px solid #ccc;
                        display: flex;
                        border-radius: 0px;
                        width: 100px;
                        justify-content: space-around;
                        align-items: center;
                        .wrapper_q {
                            display: block !important;
                        }
                    }
                    .qty {
                        width: 40px;
                        height: 15px;
                        text-align: center;
                        border: none;
                    }
                    input.qtyplus {
                        width: 25px;
                        border: none;
                        background-color: transparent;
                        display: block !important;
                    }
                    input.qtyminus {
                        width: 25px;
                        border: none;
                        background-color: transparent;
                    }
                }
                .collpase_wrapper {
                    margin-top: 50px;
                    .accordion {
                        &:nth-child(2) {
                            .card {
                                border-bottom-color: $color-brand2;
                            }
                        }
                        .card {
                            border-bottom-color: transparent;
                            background-color: transparent;
                            border-radius: 0;
                            border-left-color: transparent;
                            border-right-color: transparent;
                            .card-header {
                                background-color: transparent;
                                padding: 25px 0;
                                .btn-link,
                                .collapsed {
                                    color: $color-black;
                                    font-size: 18px;
                                    text-decoration: none;
                                    font-weight: 700;
                                    text-transform: uppercase;
                                }
                            }
                            .collapse {
                                .card-body {
                                    color: $color-font;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .trending_body1 {
        @media screen and(max-width:576px) {
            //  max-width: 400px;
        }
        .outer {
            margin: 0 auto;
        }
        #big .item {
            width: 100%;
            display: block;
        }
        #thumbs {
            @media screen and(max-width:576px) {
                display: none;
            }
        }
        #thumbs .item {
            background: #c9c9c9;
            height: 70px;
            line-height: 70px;
            padding: 0px;
            margin: 2px;
            color: #fff;
            border-radius: 3px;
            text-align: center;
            cursor: pointer;
        }
        #thumbs .item h1 {
            font-size: 18px;
        }
        .owl-theme .owl-nav [class*="owl-"] {
            -webkit-transition: all 0.3s ease;
            transition: all 0.3s ease;
        }
        .owl-theme .owl-nav [class*="owl-"].disabled:hover {
            background-color: #d6d6d6;
        }
        #big.owl-theme {
            position: relative;
        }
        #big.owl-theme .owl-next,
        #big.owl-theme .owl-prev {
            background: transparent;
            width: 22px;
            line-height: 40px;
            height: 40px;
            margin-top: -20px;
            position: absolute;
            text-align: center;
            top: 50%;
        }
        #big.owl-theme .owl-prev {
            left: 10px;
        }
        #big.owl-theme .owl-next {
            right: 10px;
        }
    }
}
.demo_h {
    text-align: center;
    font-size: 60px;
    font-weight: 700;
    color: $color-brand;
    text-transform: uppercase;
    padding-bottom: 30px;
    @media screen and(max-width:768px) {
        font-size: 30px;
    }
}
