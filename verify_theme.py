#!/usr/bin/env python3
"""
Script de verificação completa do tema Ryrox
"""

import os
import json
import glob
import re

def check_manifest():
    """Verifica o arquivo manifest"""
    print("📋 Verificando manifest...")
    
    manifest_path = 'addons/theme_ryrox/__manifest__.py'
    
    if not os.path.exists(manifest_path):
        print("  ❌ Manifest não encontrado!")
        return False
    
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica se todos os arquivos estão listados
        required_files = [
            'views/snippets/services_section.xml',
            'views/snippets/main_banner.xml',
            'views/snippets/main_product.xml',
            'views/header_templates.xml'
        ]
        
        missing_files = []
        for file_path in required_files:
            if file_path not in content:
                missing_files.append(file_path)
        
        if missing_files:
            print(f"  ⚠️  Arquivos não listados no manifest: {missing_files}")
        else:
            print("  ✅ Todos os arquivos estão no manifest")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erro ao verificar manifest: {e}")
        return False

def check_scss_files():
    """Verifica arquivos SCSS"""
    print("\n🎨 Verificando arquivos SCSS...")
    
    scss_files = [
        'addons/theme_ryrox/static/src/scss/_variables.scss',
        'addons/theme_ryrox/static/src/scss/components/_banner.scss',
        'addons/theme_ryrox/static/src/scss/components/_services.scss',
        'addons/theme_ryrox/static/src/scss/components/_header.scss',
        'addons/theme_ryrox/static/src/scss/components/_localiza-theme.scss'
    ]
    
    all_good = True
    
    for scss_file in scss_files:
        if os.path.exists(scss_file):
            try:
                with open(scss_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Verifica se tem conteúdo
                if len(content.strip()) > 50:
                    print(f"  ✅ {os.path.basename(scss_file)}: OK")
                else:
                    print(f"  ⚠️  {os.path.basename(scss_file)}: Arquivo muito pequeno")
                    all_good = False
                    
            except Exception as e:
                print(f"  ❌ Erro ao ler {scss_file}: {e}")
                all_good = False
        else:
            print(f"  ❌ {scss_file}: Não encontrado")
            all_good = False
    
    # Verifica _components.scss
    components_file = 'addons/theme_ryrox/static/src/scss/components/_components.scss'
    if os.path.exists(components_file):
        with open(components_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_imports = ['banner', 'services', 'header', 'localiza-theme']
        missing_imports = []

        for imp in required_imports:
            if f"'{imp}'" not in content and f'"{imp}"' not in content and f'./{imp}' not in content:
                missing_imports.append(imp)
        
        if missing_imports:
            print(f"  ⚠️  Imports faltando em _components.scss: {missing_imports}")
            all_good = False
        else:
            print("  ✅ _components.scss: Todos os imports presentes")
    
    return all_good

def check_templates():
    """Verifica templates XML"""
    print("\n📄 Verificando templates...")
    
    template_files = [
        'addons/theme_ryrox/views/snippets/main_banner.xml',
        'addons/theme_ryrox/views/snippets/services_section.xml',
        'addons/theme_ryrox/views/snippets/main_product.xml',
        'addons/theme_ryrox/views/header_templates.xml'
    ]
    
    all_good = True
    
    for template_file in template_files:
        if os.path.exists(template_file):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Verificações básicas
                if not content.strip().startswith('<?xml'):
                    print(f"  ⚠️  {os.path.basename(template_file)}: Não inicia com XML")
                    all_good = False
                elif not content.strip().endswith('</odoo>'):
                    print(f"  ⚠️  {os.path.basename(template_file)}: Não termina com </odoo>")
                    all_good = False
                elif len(content.strip()) < 100:
                    print(f"  ⚠️  {os.path.basename(template_file)}: Arquivo muito pequeno")
                    all_good = False
                else:
                    print(f"  ✅ {os.path.basename(template_file)}: OK")
                    
            except Exception as e:
                print(f"  ❌ Erro ao ler {template_file}: {e}")
                all_good = False
        else:
            print(f"  ❌ {template_file}: Não encontrado")
            all_good = False
    
    return all_good

def check_color_scheme():
    """Verifica esquema de cores"""
    print("\n🎨 Verificando esquema de cores...")
    
    variables_file = 'addons/theme_ryrox/static/src/scss/_variables.scss'
    
    if not os.path.exists(variables_file):
        print("  ❌ Arquivo de variáveis não encontrado")
        return False
    
    try:
        with open(variables_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica cores azuis principais
        required_colors = [
            '$color-brand2',
            '$color-brand',
            '$color-blue-600',
            '$color-hover'
        ]
        
        missing_colors = []
        for color in required_colors:
            if color not in content:
                missing_colors.append(color)
        
        if missing_colors:
            print(f"  ⚠️  Cores faltando: {missing_colors}")
            return False
        else:
            print("  ✅ Esquema de cores azuis configurado")
            return True
            
    except Exception as e:
        print(f"  ❌ Erro ao verificar cores: {e}")
        return False

def generate_report():
    """Gera relatório final"""
    print("\n📊 RELATÓRIO FINAL")
    print("=" * 50)
    
    checks = [
        ("Manifest", check_manifest()),
        ("Arquivos SCSS", check_scss_files()),
        ("Templates XML", check_templates()),
        ("Esquema de Cores", check_color_scheme())
    ]
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    print(f"\n✅ Verificações aprovadas: {passed}/{total}")
    
    if passed == total:
        print("🎉 TEMA PRONTO PARA USO!")
        print("\n🚀 Próximos passos:")
        print("1. Reinicie o servidor Odoo")
        print("2. Execute: python odoo-bin -u theme_ryrox -d sua_database")
        print("3. Ative o tema no backend do Odoo")
        print("4. Teste a página inicial")
    else:
        print("⚠️  CORREÇÕES NECESSÁRIAS")
        print("\n🔧 Verifique os itens marcados acima")
        print("📖 Consulte: docs/ryrox-localiza-theme/troubleshooting.md")
    
    return passed == total

if __name__ == "__main__":
    print("🔍 Verificação Completa - Tema Ryrox Localiza")
    print("=" * 55)
    
    success = generate_report()
    
    if success:
        print("\n✨ Verificação concluída com sucesso!")
    else:
        print("\n⚠️  Verificação concluída com problemas!")
    
    exit(0 if success else 1)
