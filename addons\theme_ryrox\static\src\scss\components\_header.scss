// Modern Header Styles - Localiza inspired
.ryrox_modern_header {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background: $color-white;
    position: sticky;
    top: 0;
    z-index: 1000;
    
    .ryrox_header_info {
        background: $color-brand2;
        color: $color-white;
        padding: 0.5rem 0;
        font-size: 0.9rem;
        
        .header_contact {
            display: flex;
            gap: 2rem;
            align-items: center;
            
            @media (max-width: 768px) {
                gap: 1rem;
                justify-content: center;
            }
            
            .contact_item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                
                i {
                    font-size: 0.9rem;
                    color: $color-hover;
                }
                
                a {
                    color: $color-white;
                    text-decoration: none;
                    transition: color 0.3s ease;
                    
                    &:hover {
                        color: $color-hover;
                    }
                }
            }
        }
        
        .header_social {
            display: flex;
            gap: 1rem;
            align-items: center;
            justify-content: flex-end;
            
            @media (max-width: 768px) {
                justify-content: center;
                margin-top: 0.5rem;
            }
            
            .social_link {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: rgba($color-white, 0.1);
                color: $color-white;
                text-decoration: none;
                transition: all 0.3s ease;
                
                &:hover {
                    background: $color-hover;
                    color: $color-brand2;
                    transform: translateY(-2px);
                }
                
                i {
                    font-size: 0.9rem;
                }
            }
        }
        
        @media (max-width: 768px) {
            text-align: center;
            
            .row > div {
                margin-bottom: 0.5rem;
            }
        }
    }
    
    .navbar {
        padding: 1rem 0;
        
        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 700;
            color: $color-brand2;
            text-decoration: none;
            
            &:hover {
                color: darken($color-brand2, 10%);
            }
        }
        
        .navbar-nav {
            .nav-link {
                font-weight: 500;
                color: $color-brand;
                padding: 0.5rem 1rem;
                margin: 0 0.25rem;
                border-radius: 5px;
                transition: all 0.3s ease;
                
                &:hover {
                    color: $color-brand2;
                    background: rgba($color-brand2, 0.1);
                }
                
                &.active {
                    color: $color-brand2;
                    background: rgba($color-brand2, 0.1);
                }
            }
        }
    }
    
    .ryrox_header_cta {
        .btn-header-cta {
            background: $color-brand2;
            border: none;
            color: $color-white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            
            &:hover {
                background: darken($color-brand2, 10%);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba($color-brand2, 0.4);
                color: $color-white;
            }
            
            i {
                margin-right: 0.5rem;
            }
        }
    }
    
    // Mobile menu enhancements
    @media (max-width: 991px) {
        .navbar-toggler {
            border: none;
            padding: 0.25rem 0.5rem;
            
            &:focus {
                box-shadow: none;
            }
        }
        
        .navbar-collapse {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .ryrox_header_cta {
            margin-top: 1rem;
            text-align: center;
        }
    }
}

// Cart link enhancements
.o_wsale_my_cart {
    position: relative;
    
    .fa-shopping-bag {
        font-size: 1.2rem;
        color: $color-brand2;
        transition: color 0.3s ease;
    }
    
    &:hover .fa-shopping-bag {
        color: darken($color-brand2, 10%);
    }
    
    .my_cart_quantity {
        position: absolute;
        top: -8px;
        right: -8px;
        background: $color-hover;
        color: $color-white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }
}
