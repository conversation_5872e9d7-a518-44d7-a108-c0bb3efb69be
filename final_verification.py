#!/usr/bin/env python3
"""
Verificação final do tema Ryrox após correções
"""

import os
import glob
import re

def check_problematic_references():
    """Verifica se ainda há referências problemáticas"""
    print("🔍 Verificando referências problemáticas...")
    
    xml_files = glob.glob('addons/theme_ryrox/views/**/*.xml', recursive=True)
    
    problematic_refs = [
        'theme_xtream.s_amazing',
        'theme_xtream.s_discount', 
        'theme_xtream.s_testimonial'
    ]
    
    issues_found = []
    
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for ref in problematic_refs:
                if ref in content:
                    issues_found.append(f"{xml_file}: {ref}")
        except Exception as e:
            print(f"  ❌ Erro ao ler {xml_file}: {e}")
    
    if issues_found:
        print("  ⚠️  Referências problemáticas encontradas:")
        for issue in issues_found:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ Nenhuma referência problemática encontrada")
        return True

def check_current_snippets():
    """Verifica snippets atualmente registrados"""
    print("\n📋 Verificando snippets registrados...")
    
    snippets_file = 'addons/theme_ryrox/views/snippets/snippets_templates.xml'
    
    if not os.path.exists(snippets_file):
        print("  ❌ Arquivo de snippets não encontrado")
        return False
    
    try:
        with open(snippets_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extrai snippets registrados
        snippet_pattern = r't-snippet="([^"]+)"'
        snippets = re.findall(snippet_pattern, content)
        
        print(f"  ✅ Snippets registrados: {len(snippets)}")
        for snippet in snippets:
            print(f"    - {snippet}")
        
        return len(snippets) > 0
        
    except Exception as e:
        print(f"  ❌ Erro ao verificar snippets: {e}")
        return False

def check_template_existence():
    """Verifica se os templates referenciados existem"""
    print("\n🔍 Verificando existência de templates...")
    
    # Templates que devem existir
    required_templates = [
        's_main',
        's_services', 
        's_main_product',
        's_new_arrivals'
    ]
    
    xml_files = glob.glob('addons/theme_ryrox/views/**/*.xml', recursive=True)
    found_templates = []
    
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for template in required_templates:
                if f'id="{template}"' in content:
                    found_templates.append(template)
                    print(f"  ✅ {template}: encontrado em {os.path.basename(xml_file)}")
        except Exception as e:
            print(f"  ❌ Erro ao ler {xml_file}: {e}")
    
    missing = set(required_templates) - set(found_templates)
    if missing:
        print(f"  ⚠️  Templates faltando: {missing}")
        return False
    
    return True

def check_manifest_integrity():
    """Verifica integridade do manifest"""
    print("\n📋 Verificando manifest...")
    
    manifest_file = 'addons/theme_ryrox/__manifest__.py'
    
    if not os.path.exists(manifest_file):
        print("  ❌ Manifest não encontrado")
        return False
    
    try:
        with open(manifest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica arquivos essenciais
        essential_files = [
            'views/snippets/snippets_templates.xml',
            'views/snippets/main_banner.xml',
            'views/snippets/services_section.xml',
            'views/emergency_fix.xml'
        ]
        
        missing_files = []
        for file_path in essential_files:
            if file_path not in content:
                missing_files.append(file_path)
        
        if missing_files:
            print(f"  ⚠️  Arquivos não listados no manifest: {missing_files}")
            return False
        else:
            print("  ✅ Todos os arquivos essenciais estão no manifest")
            return True
            
    except Exception as e:
        print(f"  ❌ Erro ao verificar manifest: {e}")
        return False

def generate_final_report():
    """Gera relatório final"""
    print("\n📊 RELATÓRIO FINAL DE VERIFICAÇÃO")
    print("=" * 50)
    
    checks = [
        ("Referências problemáticas", check_problematic_references()),
        ("Snippets registrados", check_current_snippets()),
        ("Templates existentes", check_template_existence()),
        ("Integridade do manifest", check_manifest_integrity())
    ]
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    print(f"\n✅ Verificações aprovadas: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 TEMA CORRIGIDO E PRONTO!")
        print("\n🚀 Próximos passos:")
        print("1. PARE o servidor Odoo (Ctrl+C)")
        print("2. Execute: python odoo-bin -u theme_ryrox -d sua_database --dev=reload,qweb,xml")
        print("3. Aguarde a inicialização completa")
        print("4. Teste o tema no frontend")
        print("\n💡 Se ainda houver problemas:")
        print("   - Desinstale o tema pelo backend")
        print("   - Reinicie o servidor")
        print("   - Reinstale o tema")
    else:
        print("\n⚠️  AINDA HÁ PROBLEMAS")
        print("Verifique os itens marcados acima")
    
    return passed == total

if __name__ == "__main__":
    print("🔍 VERIFICAÇÃO FINAL - Tema Ryrox")
    print("=" * 40)
    
    success = generate_final_report()
    
    if success:
        print("\n✨ Verificação final aprovada!")
    else:
        print("\n⚠️  Verificação final com problemas!")
    
    exit(0 if success else 1)
