.card-footer {
    margin-top: 90px;
    .footer_content {
        padding: 50px 0;
        .wrapper_head {
            a {
                text-decoration: none;
            }
            .made_by {
                color: $color-font;
                margin-top: 20px;
                line-height: 2;
                span {
                    margin: 0 5px;
                }
                a {
                    text-decoration: none;
                    color: $color-black;
                    &:hover {
                        color: $color-brand;
                    }
                }
                @media screen and(max-width:768px) {
                    margin-bottom: 40px;
                }
            }
        }
        .heading {
            color: $color-brand;
            font-size: 30px;
            font-weight: 700;
            letter-spacing: 12px;
            &::first-letter {
                color: $color-brand2;
            }
            hr {
                background: $color-font;
                margin: 3px 7px;
                padding-right: 5px;
            }
            p {
                color: $color-font;
                font-weight: normal;
                text-align: center;
                @media screen and(max-width:768px) {
                    text-align: left;
                }
            }
            @media screen and(max-width:768px) {
                margin-bottom: 20px;
            }
        }
        .footer_links {
            @media screen and(max-width:992px) {
                margin-top: 40px;
            }
            @media screen and(max-width:768px) {
                margin-top: 0;
            }
            ul {
                padding-left: 0;
                li {
                    padding: 5px 0;
                    display: block;
                    margin-bottom: 15px;
                    // &:last-child {
                    // }
                    a {
                        text-decoration: none;
                        font-size: 14px;
                        font-weight: bold;
                        color: $color-brand;
                        text-transform: uppercase;
                    }
                }
            }
            .scale-up-ver-center {
                &:hover {
                    animation: scale-up-ver-center 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
                }
            }
            @keyframes scale-up-ver-center {
                0% {
                    transform: scaleY(0.4);
                }
                100% {
                    transform: scaleY(1);
                }
            }
        }
        .subscribe {
            @media screen and(max-width:992px) {
                margin-top: 40px;
            }
            h4 {
                font-size: 20px;
                font-weight: 700;
                color: $color-brand;
                text-transform: uppercase;
            }
            .input-group {
                width: 100%;
                height: 50px;
                border-radius: 0;
                margin-top: 70px;
                @media screen and(max-width:768px) {
                    margin-top: 40px;
                }
                .form-control {
                    border-radius: 0;
                    height: 50px;
                }
                .input-group-text {
                    border-radius: 0;
                    background-color: $color-brand;
                    padding: 0 20px;
                    color: $color-white;
                    border: 1px solid;
                    font-weight: 700;
                }
            }
        }
        .footer_icon {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            @media screen and(max-width:768px) {
                margin-top: 40px;
            }
            a {
                color: $color-black;
                margin-right: 15px;
                @media screen and(max-width:768px) {
                    margin-right: 5px;
                }
                &:hover {
                    color: $color-font;
                }
                span {
                    font-size: 35px;
                    @media screen and(max-width:768px) {
                        font-size: 20px;
                    }
                }
            }
        }
    }
}
.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: $color-brand2;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}
