.Shop_product .btn:hover {
    background-color: transparent !important;
}
.sidebar {
    padding-right: 30px;
    .wrapper {
        .Sidebar_head {
            color: $color-brand;
            font-size: 18px;
            font-weight: 700;
            margin-top: 30px;
            margin-bottom: 40px;
        }
        .card {
            border: none;
            .card-header {
                border-color: $color-border;
                .custom-checkbox {
                    .custom-control-label {
                        font-weight: 400;
                        color: $color-brand;
                    }
                    .custom-control-label::before {
                        box-shadow: none !important;
                        border-color: -$color-brand;
                    }
                    .custom-control-input:checked ~ .custom-control-label::before {
                        color: #a5a3a3;
                        border-color: $color-brand2;
                        background-color: #ff1f49;
                        box-shadow: none !important;
                    }
                    .custom-control-input:checked ~ .custom-control-label::after {
                        background-image: none;
                    }
                }
            }
            .list-group-item {
                .custom-checkbox {
                    .custom-control-label {
                        color: $color-grey;
                    }
                    .custom-control-label::before {
                        box-shadow: none !important;
                        border-color: $color-font !important;
                    }
                    .custom-control-input:checked ~ .custom-control-label::before {
                        color: #a5a3a3;
                        border-color: $color-font;
                        background-color: #ff1f49;
                        box-shadow: none !important;
                    }
                    .custom-control-input:checked ~ .custom-control-label::after {
                        background-image: none;
                    }
                }
            }
        }
        .sidebar__filter {
            .price_wrapper {
                display: flex;
                p {
                    align-items: center;
                    margin: 0;
                    display: flex;
                    padding-right: 15px;
                    font-weight: bold;
                }
            }
        }
        .color {
            display: flex;
            padding-left: 0px;
            li {
                a:hover {
                    color: $color-brand2;
                }
                span {
                    display: block;
                    height: 27px;
                    width: 27px;
                    font-size: 20px;
                    background-color: yellow;
                    border: 1px solid;
                    border-color: transparent;
                    margin-right: 18px;
                    &:hover {
                        border: 3px solid !important;
                        border-color: $color-brand2;
                    }
                }
                P {
                    color: $color-brand;
                    padding-top: 10px;
                    font-size: 13px;
                }
                &:nth-child(2) {
                    span {
                        background-color: rgba(255, 51, 0, 0.753) !important;
                    }
                }
                &:nth-child(3) {
                    span {
                        background-color: rgba(2, 2, 2, 0.753) !important;
                    }
                }
                &:nth-child(4) {
                    span {
                        background-color: rgba(34, 148, 255, 0.753) !important;
                    }
                }
                &:nth-child(5) {
                    span {
                        background-color: rgba(0, 255, 128, 0.753) !important;
                    }
                }
            }
        }
        .size {
            display: flex;
            padding-left: 0;
            li {
                a {
                    text-decoration: none;
                }
                span {
                    font-size: 14px;
                    color: $color-brand;
                    display: block;
                    padding: 7px 6px;
                    font-size: 13px;
                    background-color: transparent;
                    font-weight: 700;
                    margin-right: 10px;
                    &:hover {
                        background-color: $color-brand2 !important;
                        color: $color-white;
                    }
                }
            }
        }
        .rec_wrapper {
            display: flex;
            margin-bottom: 25px;
            &:last-child {
                margin-bottom: 0;
            }
            .rec_img {
                max-width: 100px;
                img {
                    width: 100%;
                }
            }
            .rec_details {
                margin-left: 14px;
                h6 {
                    font-size: 17px;
                    font-weight: bold;
                    color: $color-brand;
                }
                p {
                    font-size: 16px;
                    color: $color-brand;
                }
            }
        }
    }
    .sidebar__filter {
        position: relative;
        margin-bottom: 60px;
        .section-title {
            margin-bottom: 50px;
            .borderd_header {
                text-transform: uppercase;
            }
            h4 {
                font-size: 18px;
            }
        }
    }
    #slider-range {
        margin-bottom: 30px;
        background-color: $color-grey;
        border: none;
        height: 8px;
        .ui-state-default,
        .ui-widget-content .ui-state-default {
            background-color: #3a3a3a;
            border: none;
            height: 18px;
            width: 18px;
            top: -4.8px;
            position: absolute;
        }
        &.ui-slider-horizontal .ui-slider-range {
            top: 0;
            background-color: $color-brand !important;
            left: 0%;
            width: 60%;
            position: absolute;
            height: 8px;
        }
    }
}
