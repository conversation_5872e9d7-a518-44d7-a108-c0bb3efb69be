#!/usr/bin/env python3
"""
Verificação da solução nuclear
"""

import os
import glob
import re

def verify_no_problematic_refs():
    """Verifica se não há mais referências problemáticas"""
    print("🔍 Verificando referências problemáticas...")
    
    problematic_patterns = [
        'theme_xtream.s_amazing',
        'theme_xtream.s_discount',
        'theme_xtream.s_testimonial'
    ]
    
    xml_files = glob.glob('addons/theme_ryrox/views/**/*.xml', recursive=True)
    
    issues = []
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern in problematic_patterns:
                if pattern in content:
                    issues.append(f"{xml_file}: {pattern}")
        except Exception as e:
            print(f"  ❌ Erro ao ler {xml_file}: {e}")
    
    if issues:
        print("  ❌ AINDA HÁ REFERÊNCIAS PROBLEMÁTICAS:")
        for issue in issues:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ Nenhuma referência problemática encontrada")
        return True

def verify_essential_files():
    """Verifica se arquivos essenciais existem"""
    print("\n📁 Verificando arquivos essenciais...")
    
    essential_files = [
        'addons/theme_ryrox/__manifest__.py',
        'addons/theme_ryrox/views/snippets/snippets_templates.xml',
        'addons/theme_ryrox/views/snippets/main_banner.xml',
        'addons/theme_ryrox/views/snippets/services_section.xml',
        'addons/theme_ryrox/views/snippets/main_product.xml',
        'addons/theme_ryrox/views/header_templates.xml'
    ]
    
    missing = []
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"  ✅ {os.path.basename(file_path)}")
        else:
            missing.append(file_path)
            print(f"  ❌ {file_path}")
    
    return len(missing) == 0

def verify_snippets_content():
    """Verifica conteúdo dos snippets"""
    print("\n📄 Verificando conteúdo dos snippets...")
    
    snippets_file = 'addons/theme_ryrox/views/snippets/snippets_templates.xml'
    
    if not os.path.exists(snippets_file):
        print("  ❌ Arquivo de snippets não encontrado")
        return False
    
    try:
        with open(snippets_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica se tem apenas snippets seguros
        safe_snippets = [
            'theme_ryrox.s_main',
            'theme_ryrox.s_services',
            'theme_ryrox.s_main_product'
        ]
        
        found_snippets = []
        for snippet in safe_snippets:
            if snippet in content:
                found_snippets.append(snippet)
                print(f"  ✅ {snippet}")
        
        if len(found_snippets) >= 2:  # Pelo menos 2 snippets
            print(f"  ✅ {len(found_snippets)} snippets seguros encontrados")
            return True
        else:
            print(f"  ⚠️  Apenas {len(found_snippets)} snippets encontrados")
            return False
            
    except Exception as e:
        print(f"  ❌ Erro ao verificar snippets: {e}")
        return False

def verify_manifest():
    """Verifica manifest"""
    print("\n📋 Verificando manifest...")
    
    manifest_file = 'addons/theme_ryrox/__manifest__.py'
    
    if not os.path.exists(manifest_file):
        print("  ❌ Manifest não encontrado")
        return False
    
    try:
        with open(manifest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica se não tem referências aos arquivos removidos
        removed_files = [
            'amazing.xml',
            'discount.xml',
            'testimonial.xml',
            'emergency_fix.xml'
        ]
        
        issues = []
        for removed_file in removed_files:
            if removed_file in content:
                issues.append(removed_file)
        
        if issues:
            print(f"  ⚠️  Manifest ainda referencia arquivos removidos: {issues}")
            return False
        else:
            print("  ✅ Manifest limpo")
            return True
            
    except Exception as e:
        print(f"  ❌ Erro ao verificar manifest: {e}")
        return False

def show_final_status():
    """Mostra status final"""
    print("\n📊 VERIFICAÇÃO FINAL")
    print("=" * 40)
    
    checks = [
        ("Referências problemáticas", verify_no_problematic_refs()),
        ("Arquivos essenciais", verify_essential_files()),
        ("Conteúdo dos snippets", verify_snippets_content()),
        ("Manifest", verify_manifest())
    ]
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    print(f"\n✅ Verificações: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 SOLUÇÃO NUCLEAR APLICADA COM SUCESSO!")
        print("\n🚀 PRÓXIMOS PASSOS OBRIGATÓRIOS:")
        print("1. PARE o servidor Odoo (Ctrl+C)")
        print("2. Execute: python odoo-bin -i theme_ryrox -d sua_database --dev=all")
        print("3. Aguarde inicialização completa")
        print("4. Teste o tema")
        print("\n💡 Se ainda der erro:")
        print("   - O cache pode estar no banco de dados")
        print("   - Considere criar nova database")
        print("   - Ou desinstale/reinstale o tema pelo backend")
    else:
        print("\n⚠️  AINDA HÁ PROBLEMAS")
        print("Verifique os itens marcados acima")
        print("\n🔄 ALTERNATIVA:")
        print("1. Restaure o backup: mv theme_ryrox_backup theme_ryrox")
        print("2. Reinicie o Odoo")
    
    return passed == total

if __name__ == "__main__":
    print("🔍 VERIFICAÇÃO DA SOLUÇÃO NUCLEAR")
    print("=" * 45)
    
    success = show_final_status()
    
    if success:
        print("\n✨ Verificação aprovada!")
    else:
        print("\n⚠️  Verificação com problemas!")
    
    exit(0 if success else 1)
