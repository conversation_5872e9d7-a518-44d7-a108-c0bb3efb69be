#!/usr/bin/env python3
"""
Script para corrigir referências de templates no tema Ryrox
"""

import os
import re
import glob

def fix_template_references():
    """Corrige referências de templates"""
    print("🔧 Corrigindo referências de templates...")
    
    # Arquivo de snippets
    snippets_file = 'addons/theme_ryrox/views/snippets/snippets_templates.xml'
    
    if os.path.exists(snippets_file):
        with open(snippets_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Substitui theme_xtream por theme_ryrox nas referências
        original_content = content
        content = content.replace('theme_xtream.', 'theme_ryrox.')
        
        if content != original_content:
            with open(snippets_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ Corrigido: {snippets_file}")
        else:
            print(f"  ℹ️  Já correto: {snippets_file}")
    
    # Verifica se todos os templates referenciados existem
    template_ids = [
        's_amazing',
        's_discount', 
        's_main',
        's_main_product',
        's_services',
        's_new_arrivals',
        's_testimonial'
    ]
    
    print("\n🔍 Verificando templates existentes...")
    
    for template_id in template_ids:
        found = False
        xml_files = glob.glob('addons/theme_ryrox/views/**/*.xml', recursive=True)
        
        for xml_file in xml_files:
            try:
                with open(xml_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if f'id="{template_id}"' in content:
                    print(f"  ✅ {template_id}: encontrado em {xml_file}")
                    found = True
                    break
            except Exception as e:
                print(f"  ❌ Erro ao ler {xml_file}: {e}")
        
        if not found:
            print(f"  ⚠️  {template_id}: NÃO ENCONTRADO!")

def check_xml_syntax():
    """Verifica sintaxe XML básica"""
    print("\n🔍 Verificando sintaxe XML...")
    
    xml_files = glob.glob('addons/theme_ryrox/views/**/*.xml', recursive=True)
    
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Verificações básicas
            issues = []
            
            if not content.strip().startswith('<?xml'):
                issues.append("Não inicia com declaração XML")
            
            if not content.strip().endswith('</odoo>'):
                issues.append("Não termina com </odoo>")
            
            # Verifica tags não fechadas básicas
            open_tags = re.findall(r'<(\w+)[^>]*>', content)
            close_tags = re.findall(r'</(\w+)>', content)
            
            # Remove tags auto-fechadas
            self_closing = re.findall(r'<(\w+)[^>]*/>', content)
            for tag in self_closing:
                if tag in open_tags:
                    open_tags.remove(tag)
            
            if issues:
                print(f"  ⚠️  {xml_file}: {', '.join(issues)}")
            else:
                print(f"  ✅ {xml_file}: OK")
                
        except Exception as e:
            print(f"  ❌ Erro ao verificar {xml_file}: {e}")

def show_odoo_commands():
    """Mostra comandos para reiniciar o Odoo"""
    print("\n🔄 Comandos para aplicar as correções:")
    print("1. Pare o servidor Odoo")
    print("2. Execute um dos comandos:")
    print("   python odoo-bin -u theme_ryrox -d sua_database")
    print("   python odoo-bin --dev=reload,qweb,xml -d sua_database")
    print("\n💡 Para desenvolvimento:")
    print("   python odoo-bin --dev=all -d sua_database")

if __name__ == "__main__":
    print("🚀 Correção de Referências - Tema Ryrox")
    print("=" * 45)
    
    fix_template_references()
    check_xml_syntax()
    show_odoo_commands()
    
    print("\n✨ Correções concluídas!")
