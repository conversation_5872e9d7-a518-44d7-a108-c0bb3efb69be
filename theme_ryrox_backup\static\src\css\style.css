/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/* Sections
     ========================================================================== */
/**
   * Remove the margin in all browsers.
   */
body {
  margin: 0;
}

/**
   * Render the `main` element consistently in IE.
   */
main {
  display: block;
}

.badge, .badge .text-bg-primary {
    background-color: #484bd1 !important;
    border-color: #484bd1 !important;
}

/**
   * Correct the font size and margin on `h1` elements within `section` and
   * `article` contexts in Chrome, Firefox, and Safari.
   */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
  font-family: "Poppins", sans-serif;
}

/* Grouping content
     ========================================================================== */
/**
   * 1. Add the correct box sizing in Firefox.
   * 2. Show the overflow in Edge and IE.
   */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/* Text-level semantics
     ========================================================================== */
/**
   * Remove the gray background on active links in IE 10.
   */
a {
  background-color: transparent;
  color: #484bd1;
}


/**
   * 1. Remove the bottom border in Chrome 57-
   * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
   */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  text-decoration: underline dotted;
  /* 2 */
}

/**
   * Add the correct font weight in Chrome, Edge, and Safari.
   */
b,
strong {
  font-weight: bolder;
}

/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
   * Add the correct font size in all browsers.
   */
small {
  font-size: 80%;
}

/**
   * Prevent `sub` and `sup` elements from affecting the line height in
   * all browsers.
   */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
     ========================================================================== */
/**
   * Remove the border on images inside links in IE 10.
   */
img {
  border-style: none;
}

/* Forms
     ========================================================================== */
/**
   * 1. Change the font styles in all browsers.
   * 2. Remove the margin in Firefox and Safari.
   */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
   * Show the overflow in IE.
   * 1. Show the overflow in Edge.
   */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
   * Remove the inheritance of text transform in Edge, Firefox, and IE.
   * 1. Remove the inheritance of text transform in Firefox.
   */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
   * Correct the inability to style clickable types in iOS and Safari.
   */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
   * Remove the inner border and padding in Firefox.
   */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
   * Restore the focus styles unset by the previous rule.
   */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
   * Correct the padding in Firefox.
   */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
   * 1. Correct the text wrapping in Edge and IE.
   * 2. Correct the color inheritance from `fieldset` elements in IE.
   * 3. Remove the padding so developers are not caught out when they zero out
   *    `fieldset` elements in all browsers.
   */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

/**
   * Add the correct vertical alignment in Chrome, Firefox, and Opera.
   */
progress {
  vertical-align: baseline;
}

/**
   * Remove the default vertical scrollbar in IE 10+.
   */
textarea {
  overflow: auto;
}

/**
   * 1. Add the correct box sizing in IE 10.
   * 2. Remove the padding in IE 10.
   */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
   * Correct the cursor style of increment and decrement buttons in Chrome.
   */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
   * 1. Correct the odd appearance in Chrome and Safari.
   * 2. Correct the outline style in Safari.
   */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/**
   * Remove the inner padding in Chrome and Safari on macOS.
   */
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
   * 1. Correct the inability to style clickable types in iOS and Safari.
   * 2. Change font properties to `inherit` in Safari.
   */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/* Interactive
     ===========================================
  =============================== */
/*
   * Add the correct display in Edge, IE 10+, and Firefox.
   */
details {
  display: block;
}

/*
   * Add the correct display in all browsers.
   */
summary {
  display: list-item;
}

/* Misc
     ========================================================================== */
/**
   * Add the correct display in IE 10+.
   */
template {
  display: none;
}

/**
   * Add the correct display in IE 10.
   */
[hidden] {
  display: none;
}

*:focus {
  outline: 0 !important;
}

*button:focus {
  border: none;
  outline: none;
}

* {
  list-style-type: none;
  font-family: "Poppins", sans-serif;
/*
  font-size: 14px;
*/
}

*:focus, *:active {
  outline: none !important;
}

*:hover {
  transition: 0.5s;
}

.banner_main .banner_bg {
  background-image: linear-gradient(#11111191, #11111191), url(/theme_xtream/static/src/img/bg-img/bg-1.jpg);
  justify-content: center;
  height: 100vh;
  background-size: cover;
  width: 100%;
  background-repeat: no-repeat;
}

.banner_main .card-body {
  background-color: transparent !important;
}

@media screen and (max-width: 768px) {
  .banner_main .banner_bg {
    height: 70vh;
  }
}

.banner_main .banner_bg .card {
  background: transparent;
  padding-top: 175px;
  padding-bottom: 100px;
  border: none !important;
}

@media screen and (max-width: 600px) {
  .banner_main .banner_bg .card {
    padding-left: 40px;
  }
}

@media screen and (max-width: 768px) {
  .banner_main .banner_bg .card {
    padding-top: 100px;
  }
}

.banner_main .banner_bg .card .card-title {
  color: #fff;
  font-size: 7vw;
  font-weight: bold;
  padding-bottom: 20px;
}

.banner_main .banner_bg .card .card-text {
  color: #fff;
  font-weight: 700;
  font-size: 15px;
}

.banner_main .banner_bg2 {
  background-image: linear-gradient(#11111191, #11111191), url(/theme_xtream/static/src/img/bg-img/bg-2.jpg);
  justify-content: center;
  height: 100vh;
  background-size: cover;
  width: 100%;
  background-repeat: no-repeat;
}

@media screen and (max-width: 768px) {
  .banner_main .banner_bg2 {
    height: 70vh;
  }
}

.banner_main .banner_bg2 .card {
  background: transparent;
  padding-top: 175px;
  padding-bottom: 100px;
  border: none !important;
}

@media screen and (max-width: 600px) {
  .banner_main .banner_bg2 .card {
    padding-left: 40px;
  }
}

@media screen and (max-width: 768px) {
  .banner_main .banner_bg2 .card {
    padding-top: 100px;
    padding-bottom: 0;
  }
}

.banner_main .banner_bg2 .card .card-title {
  color: #fff;
  font-size: 7vw;
  font-weight: bold;
  padding-bottom: 20px;
}

.banner_main .banner_bg2 .card .card-text {
  color: #fff;
  font-weight: 700;
  font-size: 15px;
}

.banner_main .banner_bg3 {
  background-image: linear-gradient(#11111191, #11111191), url(/theme_xtream/static/src/img/bg-img/bg-4.jpg);
  justify-content: center;
  height: 100vh;
  background-size: cover;
  width: 100%;
  background-repeat: no-repeat;
}

@media screen and (max-width: 768px) {
  .banner_main .banner_bg3 {
    height: 70vh;
  }
}

.banner_main .banner_bg3 .card {
  background: transparent;
  padding-top: 175px;
  padding-bottom: 100px;
  border: none !important;
  animation-name: fadeInOut;
  animation-delay: 1s;
  animation-duration: 3s;
}

@media screen and (max-width: 600px) {
  .banner_main .banner_bg3 .card {
    padding-left: 40px;
  }
}

@media screen and (max-width: 768px) {
  .banner_main .banner_bg3 .card {
    padding-top: 50px;
    padding-bottom: 100px;
  }
}

.banner_main .banner_bg3 .card .card-title {
  color: #fff;
  font-size: 5vw;
  font-weight: bold;
  padding-bottom: 20px;
  text-transform: uppercase;
}

.banner_main .banner_bg3 .card .card-text {
  color: #fff;
  padding-bottom: 30px;
  font-size: 14px;
}

.banner_main .banner_bg3 .breadcrumb {
  background: transparent;
  padding-top: 110px;
  padding-bottom: 110px;
}

.banner_main .banner_bg3 .breadcrumb .breadcrumb-item {
  color: #e9c939;
}

.banner_main .banner_bg3 .breadcrumb .breadcrumb-item:first-child::before {
  display: none;
}

.banner_main .banner_bg3 .breadcrumb .breadcrumb-item::before {
  display: inline-block;
  padding-right: 1.5rem;
  color: #fff;
  content: "/";
}

.banner_main .banner_bg3 .breadcrumb .breadcrumb-item a {
  color: #fff;
  text-decoration: none;
}

.banner_main .banner_bg3 .breadcrumb .breadcrumb-item a:hover {
  color: #e9c939;
}

.banner_main .banner_bg4 {
  background-image: url(/theme_xtream/static/src/images/banner/home.jpg);
  justify-content: center;
  max-width: 1400px;
  margin: auto;
  background-size: cover;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  margin-top: 134px;
}

.banner_main .banner_bg4 .breadcrumb {
  background: transparent;
  padding-top: 110px;
  padding-bottom: 110px;
}

.banner_main .banner_bg4 .breadcrumb .breadcrumb-item {
  color: #e9c939;
}

.banner_main .banner_bg4 .breadcrumb .breadcrumb-item:first-child::before {
  display: none;
}

.banner_main .banner_bg4 .breadcrumb .breadcrumb-item::before {
  display: inline-block;
  padding-right: 1.5rem;
  color: #fff;
  content: "/";
}

.banner_main .banner_bg4 .breadcrumb .breadcrumb-item a {
  color: #fff;
  text-decoration: none;
}

.banner_main .banner_bg4 .breadcrumb .breadcrumb-item a:hover {
  color: #e9c939;
}

.banner_main .owl-carousel button.owl-dot span {
  height: 10px;
  width: 10px;
  color: #fff;
  background-color: #fff;
  border-radius: 50%;
  display: block;
  font-weight: 700;
  margin: 5px;
}

.banner_main .owl-carousel button.owl-dot.active span {
  background-color: #484bd1;
}

.banner_main .owl-carousel .owl-dots {
  position: absolute;
  bottom: 250px;
  left: 40px;
  transform: rotate(89deg);
  background-color: transparent;
}

@media screen and (max-width: 1150px) {
  .banner_main .owl-carousel .owl-dots {
    left: 0 !important;
  }
}

.slideInDown {
  animation-name: slideInDown;
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes slideInDown {
  0% {
    transform: translateY(-100%);
    visibility: visible;
  }
  100% {
    transform: translateY(0);
  }
}

.tada {
  animation-name: tada;
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

.slideInUp {
  animation-name: slideInUp;
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes slideInUp {
  0% {
    transform: translateY(100%);
    visibility: visible;
  }
  100% {
    transform: translateY(0);
  }
}

.slideInLeft {
  animation-name: slideInLeft;
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    visibility: visible;
  }
  100% {
    transform: translateX(0);
  }
}

.fadeInDownBig {
  animation-name: fadeInDownBig;
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes fadeInDownBig {
  0% {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}

.fadeInLeftBig {
  animation-name: fadeInLeftBig;
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes fadeInLeftBig {
  0% {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}

.btn {
  border: none !important;
  outline: 0 !important;
  transition: 0.5s;
  box-shadow: none !important;
  font-weight: 400;
  font-family: "Poppins", sans-serif;
}

.btn-primary {
  background-color: transparent !important;
  color: #484bd1 !important;
  border-radius: 0 !important;
  border-color: #484bd1 !important;
  font-size: 16px;
  font-weight: 600;
  border: 3px solid !important;
}

.btn-primary:hover {
  border-color: #484bd1 !important;
  color: #fff !important;
  background: #484bd1 !important;
}

.btn-link {
  background-color: transparent !important;
  color: #484bd1 !important;
  border-radius: 0 !important;
  border-color: #484bd1 !important;
  font-size: 16px;
  font-weight: 600;
/*
  border: 2px solid !important;
*/
}

.btn-link:hover {
  border-color: #484bd1 !important;
  color: black !important;
/*
  background: #484bd1 !important;
*/
}


.btn-secondary{
  background-color: white !important;
  color: #031B09 !important;
  border-radius: 0 !important;
  border: 2px solid !important;
  border-color: grey !important;
}

#special {
  background-color: transparent !important;
  border-color: #fff;
  padding: 12px 36px;
  color: #fff !important;
  font-size: 16px;
  font-weight: 600;
  border-radius: 0;
  border: 3px solid !important;
}

#special:hover {
  border-color: #484bd1 !important;
  color: #fff !important;
  background: #484bd1 !important;
}

.container .row #o_cart_summary .card .card-body .btn-secondary{
  background-color: #484bd1 !important;
  color: #fff !important;
  border: 0px solid !important;
}

.container .row #o_cart_summary .card .card-body .btn-secondary:hover{
  border: 0px solid !important;
  color: #fff !important;
  background: #031B09 !important;
}


.btn:focus, .btn.focus {
  outline: 0;
}

.btn-cart {
  background-color: #031B09 !important;
  margin-right: 5px;
  padding: 2px 9px;
  color: #fff !important;
  font-size: 13px;
  border-radius: 0 !important;
  border: none;
}

.btn-checkout {
  background-color: #484bd1 !important;
  padding: 2px 9px;
  color: #fff !important;
  font-size: 13px;
  border-radius: 0 !important;
  border: none;
}

.btn-details {
  background-color: transparent !important;
  padding: 2px 9px;
  color: #fff !important;
  font-size: 13px;
  border-radius: 0 !important;
  border: 1px solid !important;
  border-color: #fff !important;
  border-radius: 50% !important;
}

.btn-checkout_m {
  background-color: #484bd1 !important;
  text-decoration: none !important;
  padding: 7px 12px;
  color: #fff !important;
  font-size: 13px;
  border-radius: 0 !important;
  border: none;
}

.btn-cart_p {
  background-color: #484bd1 !important;
  color: #fff !important;
  font-size: 13px;
  border-radius: 0 !important;
  border: none;
  padding: 13px 40px;
  margin-left: 33px;
}

.btn-shopping {
  background-color: transparent !important;
  color: #031B09 !important;
  font-size: 20px;
  font-weight: 700;
  border-radius: 0 !important;
  border: 2px solid !important;
  border-color: #031B09 !important;
  padding: 13px 40px;
  margin-left: 33px;
}

@media screen and (max-width: 768px) {
  .btn-shopping {
    padding: 8px 15px;
    font-size: 2vw;
  }
}

.btn-clear {
  background-color: transparent !important;
  color: #9f9f9f !important;
  font-size: 20px;
  font-weight: 700;
  border-radius: 0 !important;
  border: 2px solid !important;
  border-color: #dedede !important;
  padding: 13px 40px;
  margin-left: 33px;
}

@media screen and (max-width: 768px) {
  .btn-clear {
    padding: 8px 15px;
    margin-bottom: 10px;
    font-size: 2vw;
  }
}

.btn-update {
  background-color: #f4f2f8 !important;
  color: #9f9f9f !important;
  font-size: 20px;
  font-weight: 700;
  border-radius: 0 !important;
  border: 2px solid !important;
  border-color: #f4f2f8 !important;
  padding: 13px 40px;
  margin-left: 33px;
}

@media screen and (max-width: 768px) {
  .btn-update {
    padding: 8px 15px;
    font-size: 2vw;
  }
}

.btn-checkout_c {
  text-transform: uppercase;
  background-color: #484bd1 !important;
  text-decoration: none !important;
  width: 100%;
  height: 60px;
  color: #fff !important;
  font-size: 16px;
  font-weight: 700;
  border-radius: 0 !important;
  border: none;
  line-height: 50px;
  height: 60px;
}

.btn-checkout_c:hover {
  background-color: #031B09 !important;
}

.btn-contact {
  margin-top: 5px;
  text-transform: uppercase;
  background-color: #484bd1 !important;
  text-decoration: none !important;
  width: 100%;
  height: 40px !important;
  color: #fff !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  border-radius: 0 !important;
  border: none;
  line-height: 0px;
}

.btn-contact:hover {
  background-color: #031B09 !important;
}

.product {
  margin-top: 90px;
  padding-bottom: 90px;
  /* Add padding BETWEEN each column */
  /* Create three equal columns that floats next to each other */
  /* Clear floats after rows */
  /* Content */
  /* The "show" class is added to the filtered elements */
  /* Style the buttons */
}

.product .main {
  padding-top: 30px;
  margin: auto;
}

.product .main h2 {
  text-align: center;
  font-size: 60px;
  font-weight: 700;
  color: #031B09;
  text-transform: uppercase;
  padding-bottom: 30px;
}

@media screen and (max-width: 768px) {
  .product .main h2 {
    font-size: 30px;
  }
}

.product .main .categories {
  display: flex;
  justify-content: center;
}

@media screen and (max-width: 768px) {
  .product .main .categories {
    display: block;
  }
}

.product h1 {
  font-size: 50px;
  word-break: break-all;
}

.product .row {
  margin: 10px -16px;
}

.product .row,
.product .row > .column {
  padding: 8px;
}

.product .column {
  float: left;
  width: 33.33%;
  display: none;
  /* Hide all elements by default */
}
.product .row .product_column {
  float: left;
/*
  width: 33.33%;
*/
  /* Hide all elements by default */
}

.product .row:after {
  content: "";
  display: table;
  clear: both;
}

.product .content {
  padding: 50px 0;
}

.product .content .img_zoom {
  overflow: hidden;
}

.product .content .wrapper {
  max-width: 330px;
  position: relative;
}

.product .content .wrapper:hover .img_details {
  position: absolute;
  left: 44%;
  display: block;
  bottom: 45%;
  z-index: 3;
}

@media screen and (max-width: 992px) {
  .product .content .wrapper:hover{
    left: 35%;
    bottom: 43%;
  }
}

@media screen and (max-width: 992px) {
  .product .content .wrapper{
    left: 25%;
    bottom: 43%;
    max-width: 80%;
    height: 270px;
  }
}
@media screen and (max-width: 992px) {
  .product .content .wrapper:hover{
    left: 44%;
    bottom: 43%;
  }
}
@media screen and (max-width: 992px) {
  .product .content .wrapper:hover{
    font-size: 12px;
  }
}
@media screen and (max-width: 576px) {
  .product .content .wrapper {
    max-width: 60%;
    height: 250px;
  }
}
.product .content .wrapper_product {
    max-width: 330px;
    position: relative;
    height: 330px;
}



@media screen and (max-width: 992px) {
  .product .content .wrapper:hover .img_details {
    left: 44%;
    bottom: 43%;
  }
}

.product .content .wrapper:hover .img_details i {
  font-size: 25px;
  color: #fff;
  padding: 4px 2px;
}

@media screen and (max-width: 992px) {
  .product .content .wrapper:hover .img_details i {
    font-size: 12px;
  }
}

.product .content .wrapper:hover:after {
  position: absolute;
  content: " ";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: #00000054 !important;
}

.product .content .wrapper:before {
  position: absolute;
  content: " ";
  display: block;
  top: 50%;
  left: 50%;
}

@media screen and (max-width: 576px) {
  .product .content .wrapper {
    max-width: none;
  }
}

.product .content .wrapper .img_details {
  display: none;
}

.product .content .wrapper img {
  width: 100%;
}

.product .content p {
  color: #9f9f9f;
  font-size: 25px;
  font-weight: lighter;
  margin-top: 20px;
  margin-bottom: 5px;
}

.product .content h6 {
  color: #031B09;
  line-height: 1.5;
  font-weight: 400;
  font-size: 15px;
  letter-spacing: 1px;
  margin-bottom: 40px;
}

.product .content a {
  font-size: 13px;
  font-weight: 700;
  color: #484bd1;
  text-decoration: none;
}

.product .content a:hover {
  color: #031B09;
}

.product .content #zoomIn {
  transform: scale(1);
  transition: .3s ease-in-out;
    transform: scale(1);
}

.product .content #zoomIn:hover {
  transform: scale(1.05);
  border-radius: 6px 6px 0px 0px;
}

#zoomIn:hover {
  transform: scale(1.05);
  border-radius: 6px 6px 0px 0px;
}

.product .show {
  display: block;
}

.product .btn {
  border: none;
  outline: none;
  padding: 12px 16px;
  background-color: white;
  cursor: pointer;
  color: #9f9f9f;
  font-weight: 700;
}

.product .btn:hover {
  background-color: #ddd;
}

/*.product .btn.active {
  color: #031B09;
}*/

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0;
  outline: 0;
  margin: auto;
}

@media screen and (max-width: 768px) {
  .modal-content {
    margin: auto;
    max-width: 65%;
  }
}

@media screen and (max-width: 375px) {
  .modal-content {
    margin: auto;
    max-width: 95% !important;
    width: 100% !important;
  }
}

.modal-dialog {
  max-width: 660px;
  margin: 1.75rem auto;
}

@media screen and (max-width: 768px) {
  .modal-dialog {
    margin: 50px 50px;
  }
}

.modal .model-body {
  padding: 20px 25px;
}

.modal .wrapper {
  max-width: 100% !important;
}

.modal .wrapper img {
  width: 100%;
}

.modal .model_details h4 {
  color: #031B09;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 10px;
}

.modal .model_details .model_rate ul {
  padding-left: 0;
  display: flex;
}

.modal .model_details .model_rate ul span {
  color: #ffcd07;
}

.modal .model_details .price {
  color: #031B09;
  font-size: 20px;
  font-weight: 700;
}

.modal .model_details .price span {
  margin-left: 5px;
  font-weight: 700;
  font-size: 16;
  color: #6c6a74;
  text-decoration: line-through;
}

.modal .model_details p {
  color: #031B09;
  line-height: 24px;
}

.modal .model_details a {
  text-decoration: underline;
}

.modal .model_details .product_count {
  margin-top: 30px;
  display: flex;
}

@media screen and (max-width: 412px) {
  .modal .model_details .product_count {
    display: grid !important;
    grid-row-gap: 20px !important;
  }
}

.modal .model_details .product_count #myform {
  margin-right: 10px;
  text-align: center;
  display: flex;
  width: 100px;
}

.modal .model_details .product_count .qty {
  width: 40px;
  height: 25px;
  text-align: center;
  border: none;
}

.modal .model_details .product_count input.qtyplus {
  width: 25px;
  height: 25px;
  background-color: transparent;
  border: none;
}

.modal .model_details .product_count input.qtyminus {
  width: 25px;
  height: 25px;
  background-color: transparent;
  border: none;
}

.modal .model_details .product_count .icons {
  margin-left: 15px;
}

.modal .model_details .product_count .icons span {
  background: #484bd1;
  color: #fff;
  font-size: 16px;
  padding: 9px 10px;
}

.modal .model_details .product_count .icons span:hover {
  background-color: #000000;
}

.modal .model_details .product_count .icons span:last-child {
  background-color: #00bcd4 !important;
  margin-left: 15px;
}

.modal .model_details .share {
  font-size: 14px;
  font-weight: 400;
  padding-top: 20px;
}

.modal .model_details .footer_icon {
  padding-top: 5px;
}

.modal .model_details .footer_icon a {
  color: #9f9f9f;
  margin-right: 15px;
}

.modal .model_details .footer_icon a:hover {
  color: #000000;
}

.top_nav {
  margin: 0px 0;
}

.top_nav .wrapper {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  align-items: center;
}

@media screen and (max-width: 572px) {
  .top_nav .wrapper {
    display: block;
  }
}

.top_nav .wrapper .nav_center {
  margin: auto;
}

@media screen and (max-width: 992px) {
  .top_nav .wrapper .nav_center {
    margin: 0;
  }
}

.top_nav .wrapper .nav_center a {
  text-decoration: none;
}

.heading {
  color: #031B09;
  font-size: 30px;
  font-weight: 700;
  letter-spacing: 12px;
  /*padding-left: 450px;
  padding-right: 450px;*/
}
@media screen and (max-width: 572px) {
  .heading {
    text-align: center;
  }
}

@media screen and (max-width: 768px) {
  .heading {
    font-size: 3vw;
  }
}

.heading::first-letter {
  color: #484bd1;
}
.o_header_standard.o_header_affixed.o_header_is_scrolled{
background-color: #f7f7f7;
}

.heading hr {
  background: #9f9f9f;
  margin: 3px 7px;
  padding-right: 5px;
}

.heading p {
  color: #9f9f9f;
  font-weight: normal;
  text-align: center;
}

@media screen and (max-width: 572px) {
  .heading p {
    font-size: 12px;
  }
}

/*.top_nav .wrapper .nav_center .heading {
  color: #031B09;
  font-size: 30px;
  font-weight: 700;
  letter-spacing: 12px;
  padding-left: 450px;
  padding-right: 450px;
}*/


.top_nav .wrapper .nav_right {
  display: flex;
  align-items: center;
}

@media screen and (max-width: 572px) {
  .top_nav .wrapper .nav_right {
    justify-content: center;
  }
}

.top_nav .wrapper .nav_right .bag {
  position: relative;
}

.top_nav .wrapper .nav_right .bag span {
  color: #fff;
  background-color: #484bd1;
  height: 20px;
  width: 20px;
  font-size: 13px;
  position: absolute;
  left: -8px;
  top: 12px;
  border-radius: 50%;
}

.top_nav .wrapper .nav_right .dropdown i {
  font-size: 20px;
  padding-right: 7px;
}

.top_nav .wrapper .nav_right .dropdown .d_image {
  display: block;
  max-width: 40px;
}

.top_nav .wrapper .nav_right .dropdown .d_image img {
  width: 100%;
}

.top_nav .wrapper .nav_right .dropdown .dropdown-menu {
  padding: 30px 20px;
}

.top_nav .wrapper .nav_right .dropdown .dropdown-menu .nav_product {
  color: #000000;
  margin-top: 10px;
}

.top_nav .wrapper .nav_right .dropdown .dropdown-menu .nav_product span {
  color: #031B09;
}

.top_nav .wrapper .nav_right .dropdown .dropdown-menu .drop_buttons {
  margin-top: 10px;
}

.top_nav .wrapper .nav_right .dropdown .dropdown-menu .drop_buttons span {
  padding-left: 5px;
}

.top_nav .wrapper .nav_right .side_b {
  background-color: #031B09;
  color: #fff;
  border-radius: 50%;
  padding-top: 5px;
  font-size: 30px;
  cursor: pointer;
  height: 55px;
  display: block;
  width: 55px;
  padding-left: 14px;
}

@media screen and (max-width: 768px) {
  .top_nav .wrapper .nav_right .side_b {
    background-color: #031B09;
    color: #fff;
    border-radius: 50%;
    padding-top: 6px !important;
    font-size: 15px !important;
    cursor: pointer;
    height: 35px;
    display: block;
    width: 35px;
    padding-left: 11px !important;
  }
}

.navigation {
  padding: 35px 0 50px 0px;
}

.navigation .help-line {
  background-color: #484bd1;
  width: auto;
  height: 35px;
  padding: 0 30px;
  display: block;
  line-height: 35px;
  font-size: 14px;
  font-weight: 600;
}

.navigation .help-line a {
  color: #fff;
  text-decoration: none;
}

.navigation .help-line:hover {
  background-color: #031B09;
}
.help-line {
  background-color: #484bd1;
  width: auto;
  height: 35px;
  padding: 0 30px;
  display: block;
  line-height: 35px;
  font-size: 14px;
  font-weight: 600;
}

.help-line a {
  color: #fff !important;
  text-decoration: none !important;
}

.help-line:hover {
  background-color: #031B09;
}

.navbar-dark {
  padding: 20px 0;
}

@media screen and (max-width: 768px) {
  .navbar-dark {
    display: grid;
    grid-template-columns: 1fr;
    grid-row-gap: 30px;
  }
}

.navbar-dark .navbar-toggler {
  border: 2px solid;
  border-color: #484bd1;
  background-color: #484bd1;
  border-radius: 0;
}

.navbar-dark .navbar-toggler .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='white' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

@media screen and (max-width: 768px) {
  .navbar-dark .navbar-brand {
    margin: auto;
  }
}

.navbar-dark .navbar-brand .footer_icon {
  position: relative;
}

.navbar-dark .navbar-brand .footer_icon .link_top {
  color: #fff;
  background: #484bd1;
  font-size: 11px;
  padding: 5px 12px;
  font-weight: 700;
  position: absolute;
  top: -43px;
  left: 9px;
}

.navbar-dark .navbar-brand .footer_icon .link_top:after {
  position: absolute;
  z-index: 2;
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 9px 9px 9px;
  border-color: transparent transparent #484bd1 transparent;
  bottom: -2px;
  left: -6px;
  transform: rotate(-45deg);
}

.navbar-dark .navbar-brand a {
  color: #000000;
  margin-right: 15px;
}

.navbar-dark .navbar-brand a:hover {
  color: #9f9f9f;
}

.navbar-nav {
  text-align: center;
  position: relative;
}

.navbar-nav .link_top {
  color: #fff;
  background: #484bd1;
  font-size: 11px;
  padding: 5px 15px;
  font-weight: 700;
  position: absolute;
  top: -29px;
  left: 221px;
}

.navbar-nav .link_top:after {
  position: absolute;
  z-index: 2;
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 9px 9px 9px;
  border-color: transparent transparent #7BF098 transparent;
  bottom: -2px;
  left: -6px;
  transform: rotate(-45deg);
}

@media screen and (max-width: 992px) {
  .navbar-nav .nav-item .dropdown-menu {
    text-align: center;
  }
}

.navbar-nav .nav-item .dropdown-menu .dropdown-item {
  padding: 10px 10px;
}

.navbar-nav .nav-item .dropdown-menu .dropdown-item:hover {
  color: #031B09;
}

.navbar-nav .nav-item .nav-link {
  color: rgba(0,0,0,.5);
  padding-right: 15px;
  padding-left: 15px;
  position: relative;
  z-index: 1;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
}

@media screen and (max-width: 992px) {
  .navbar-nav .nav-item .nav-link {
    padding: 10px 0;
  }
}

.navbar-nav .nav-item.active .nav-link {
  color: #031B09 !important;
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
  color: #9f9f9f !important;
}

/* ============ desktop view ============ */
@media all and (min-width: 992px) {
  .navbar .nav-item .dropdown-menu {
    display: none;
  }
  .navbar .nav-item:hover .nav-link {
    color: #181818;
  }
  .navbar .nav-item:hover .dropdown-menu {
    display: block;
    border: none;
  }
  .navbar .nav-item .dropdown-menu {
    margin-top: 0;
    padding: 20px 10px;
  }
}

/* ============ desktop view .end// ============ */
#arrow {
  position: relative;
  top: 0;
  transition: top ease 0.5s;
}

#arrow:hover {
  top: -5px;
}

.navbar-dark .navbar-nav .nav-item .dropdown-menu .dropdown-item:active {
  background-color: #484bd1;
  color: #fff;
}

.sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 4;
  top: 0;
  left: 0;
  background-color: #fff;
  overflow-x: hidden;
  transition: 0.5s;
  padding-top: 60px;
}

.sidenav a {
  padding: 8px 8px 8px 32px;
  text-decoration: none;
  font-size: 25px;
  color: #031B09;
  display: block;
  transition: 0.3s;
}

.sidenav a:hover {
  color: #484bd1;
}

.sidenav .closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px;
  margin-left: 50px;
}

#main {
  transition: margin-left 0.5s;
  padding: 16px;
}

@media screen and (max-height: 450px) {
  .sidenav {
    padding-top: 15px;
  }
  .sidenav a {
    font-size: 18px;
  }
}
.o_footer {
    background-color: transparent !important;
    /*color: #000000 !important;*/
}
.card-footer {
  margin-top: 0px;
  padding-bottom: 50px !important;
  padding-top: 50px !important;
}

.card-footer .footer_content {
  padding: 0px 0;
}

.card-footer .footer_content .wrapper_head a {
  text-decoration: none;
}

.card-footer .footer_content .wrapper_head .made_by {
  color: #9f9f9f;
  margin-top: 20px;
  line-height: 2;
}

.card-footer .footer_content .wrapper_head .made_by span {
  margin: 0 5px;
}

.card-footer .footer_content .wrapper_head .made_by a {
  text-decoration: none;
  color: #000000;
}

.card-footer .footer_content .wrapper_head .made_by a:hover {
  color: #031B09;
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .wrapper_head .made_by {
    margin-bottom: 40px;
  }
}

.card-footer .footer_content .heading {
  color: #031B09;
  font-size: 30px;
  font-weight: 700;
  letter-spacing: 12px;
}

.card-footer .footer_content .heading::first-letter {
  color: #484bd1;
}

.card-footer .footer_content .heading hr {
  background: #9f9f9f;
  margin: 3px 7px;
  padding-right: 5px;
}

.card-footer .footer_content .heading p {
  color: #9f9f9f;
  font-weight: normal;
  text-align: center;
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .heading p {
    text-align: left;
  }
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .heading {
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 992px) {
  .card-footer .footer_content .footer_links {
    margin-top: 40px;
  }
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .footer_links {
    margin-top: 0;
  }
}

.card-footer .footer_content .footer_links ul {
  padding-left: 0;
}

.card-footer .footer_content .footer_links ul li {
  padding: 5px 0;
  display: block;
  margin-bottom: 15px;
}

.card-footer .footer_content .footer_links ul li a {
  text-decoration: none;
  font-size: 14px;
  font-weight: bold;
  color: #031B09;
  text-transform: uppercase;
}

.card-footer .footer_content .footer_links .scale-up-ver-center:hover {
  animation: scale-up-ver-center 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}

@keyframes scale-up-ver-center {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@media screen and (max-width: 992px) {
  .card-footer .footer_content .subscribe {
    margin-top: 40px;
  }
}

.card-footer .footer_content .subscribe h4 {
  font-size: 20px;
  font-weight: 700;
  color: #031B09;
  text-transform: uppercase;
}

.card-footer .footer_content .subscribe .input-group {
  width: 100%;
  height: 50px;
  border-radius: 0;
  margin-top: 70px;
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .subscribe .input-group {
    margin-top: 40px;
  }
}

.card-footer .footer_content .subscribe .input-group .form-control {
  border-radius: 0;
  height: 50px;
}

.card-footer .footer_content .subscribe .input-group .input-group-text {
  border-radius: 0;
  background-color: #031B09;
  padding: 0 20px;
  color: #fff;
  border: 1px solid;
  font-weight: 700;
}

.card-footer .footer_content .footer_icon {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .footer_icon {
    margin-top: 40px;
  }
}

.card-footer .footer_content .footer_icon a {
  color: #000000;
  margin-right: 15px;
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .footer_icon a {
    margin-right: 5px;
  }
}

.card-footer .footer_content .footer_icon a:hover {
  color: #9f9f9f;
}

.card-footer .footer_content .footer_icon a span {
  font-size: 35px;
}

@media screen and (max-width: 768px) {
  .card-footer .footer_content .footer_icon a span {
    font-size: 20px;
  }
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #484bd1;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.Shop_product .btn:hover {
  background-color: transparent !important;
}

.sidebar {
    padding-right: 10px;
}

.sidebar .wrapper .Sidebar_head {
  color: #031B09;
  font-size: 18px;
  font-weight: 700;
  margin-top: 5px;
  margin-bottom: 15px;
}

.sidebar .sidebar__filter .price_wrapper .price_filter_button {
  background-color: transparent !important;
  color: #031B09 !important;
  border-radius: 0 !important;
  font-weight: 550;
  font-size: 12px;
  border: 2px solid !important;
}

.sidebar .sidebar__filter .price_wrapper .price_filter_button:hover {
  border-color: #031B09 !important;
  color: #fff !important;
  background: #031B09 !important;
}

.brand{
  text-decoration: none !important;
}

.sidebar .wrapper .card {
  border: none;
}

#wsale_products_attributes_collapse .js_attributes .nav .nav-item strong{
  font-family: "Poppins", sans-serif !important;
}

.sidebar .wrapper .card .card-header .nav-item .nav-link.active, .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #484bd1;
}
.sidebar .wrapper .card .card-header .nav-item a {
    color: #031B09;
}

.sidebar .wrapper .card .card-header {
  border-color: #dedede;
}

.sidebar .wrapper .card .card-header label {
  font-weight: 400 !important;
  color: #031B09 !important;
}

.sidebar .wrapper .card .card-header .custom-checkbox .custom-control-label {
  font-weight: 400;
  color: #031B09;
}

.sidebar .wrapper .card .card-header .custom-checkbox .custom-control-label::before {
  box-shadow: none !important;
  border-color: -#031B09;
}

.sidebar .wrapper .card .card-header .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  color: #a5a3a3;
  border-color: #484bd1;
  background-color: #ff1f49;
  box-shadow: none !important;
}

.sidebar .wrapper .card .card-header .custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: none;
}

.sidebar .wrapper .card .list-group-item .custom-checkbox .custom-control-label {
  color: #6c6a74;
}

.sidebar .wrapper .card .list-group-item .custom-checkbox .custom-control-label::before {
  box-shadow: none !important;
  border-color: #9f9f9f !important;
}

.sidebar .wrapper .card .list-group-item .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  color: #a5a3a3;
  border-color: #9f9f9f;
  background-color: #ff1f49;
  box-shadow: none !important;
}

.sidebar .wrapper .card .list-group-item .custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: none;
}

.sidebar .wrapper .sidebar__filter .price_wrapper {
  display: flex;
}

.sidebar .wrapper .sidebar__filter .price_wrapper p {
  align-items: center;
  margin: 0;
  display: flex;
  padding-right: 2%;
  font-weight: bold;
}
.sidebar .wrapper .sidebar__filter .price_wrapper span {
  align-items: center;
  margin-left: 5%;
  display: flex;
  padding-right: 5%;
  font-weight: bold;
}

.sidebar .wrapper .color {
  display: flex;
  padding-left: 0px;
}

.sidebar .wrapper .color li a:hover {
  color: #484bd1;
}

.sidebar .wrapper .color li span {
  display: block;
  height: 27px;
  width: 27px;
  font-size: 20px;
  background-color: yellow;
  border: 1px solid;
  border-color: transparent;
  margin-right: 18px;
}

.sidebar .wrapper .color li span:hover {
  border: 3px solid !important;
  border-color: #484bd1;
}

.sidebar .wrapper .color li P {
  color: #031B09;
  padding-top: 10px;
  font-size: 13px;
}

.sidebar .wrapper .color li:nth-child(2) span {
  background-color: rgba(255, 51, 0, 0.753) !important;
}

.sidebar .wrapper .color li:nth-child(3) span {
  background-color: rgba(2, 2, 2, 0.753) !important;
}

.sidebar .wrapper .color li:nth-child(4) span {
  background-color: rgba(34, 148, 255, 0.753) !important;
}

.sidebar .wrapper .color li:nth-child(5) span {
  background-color: rgba(0, 255, 128, 0.753) !important;
}

.sidebar .wrapper .size {
  display: flex;
  padding-left: 0;
}

.sidebar .wrapper .size li a {
  text-decoration: none;
}

.sidebar .wrapper .size li span {
  font-size: 14px;
  color: #031B09;
  display: block;
  padding: 7px 6px;
  font-size: 13px;
  background-color: transparent;
  font-weight: 700;
  margin-right: 10px;
}

.sidebar .wrapper .size li span:hover {
  background-color: #484bd1 !important;
  color: #fff;
}

.sidebar .wrapper .rec_wrapper {
  display: flex;
  margin-bottom: 25px;
}

.sidebar .wrapper .rec_wrapper:last-child {
  margin-bottom: 0;
}

.sidebar .wrapper .rec_wrapper .rec_img {
  max-width: 100px;
}

.sidebar .wrapper .rec_wrapper .rec_img img {
  width: 100%;
}

.sidebar .wrapper .rec_wrapper .rec_details {
  margin-left: 14px;
}

.sidebar .wrapper .rec_wrapper .rec_details h6 {
  font-size: 17px;
  font-weight: bold;
  color: #031B09;
}

.sidebar .wrapper .rec_wrapper .rec_details p {
  font-size: 16px;
  color: #031B09;
}

.sidebar .sidebar__filter {
  position: relative;
  margin-bottom: 60px;
}

.sidebar .sidebar__filter .section-title {
  margin-bottom: 50px;
}

.sidebar .sidebar__filter .section-title .borderd_header {
  text-transform: uppercase;
}

.sidebar .sidebar__filter .section-title h4 {
  font-size: 18px;
}

.sidebar #slider-range {
  margin-bottom: 30px;
  background-color: #6c6a74;
  border: none;
  height: 8px;
}

.sidebar #slider-range .ui-state-default,
.sidebar #slider-range .ui-widget-content .ui-state-default {
  background-color: #3a3a3a;
  border: none;
  height: 18px;
  width: 18px;
  top: -4.8px;
  position: absolute;
}

.sidebar #slider-range.ui-slider-horizontal .ui-slider-range {
  top: 0;
  background-color: #031B09 !important;
  left: 0%;
  width: 60%;
  position: absolute;
  height: 8px;
}

.main_product .wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

@media screen and (max-width: 768px) {
  .main_product .wrapper {
    grid-template-columns: 1fr;
  }
}

.main_product .wrapper .main_left {
  position: relative;
  background-size: cover;
  width: 100%;
  background-repeat: no-repeat;
}

.main_product .wrapper .main_left:hover {
  transition: 0.5s;
}

.main_product .wrapper .main_left:hover:after {
  position: absolute;
  content: " ";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: #7bf09885 !important;
}

.main_product .wrapper .main_left .card {
  background: transparent;
  padding-top: 112px;
  padding-bottom: 100px;
  border: none !important;
  margin-left: 65px;
  z-index: 3;
}

@media screen and (max-width: 768px) {
  .main_product .wrapper .main_left .card {
    padding-top: 100px;
  }
}

.main_product .wrapper .main_left .card .card-title {
  color: #fff;
  font-size: 5vw;
  font-weight: bold;
  padding-bottom: 20px;
  text-transform: uppercase;
}

@media screen and (max-width: 768px) {
  .main_product .wrapper .main_left .card .card-title {
    font-size: 30px !important;
  }
}

.main_product .wrapper .main_left .card .card-text {
  color: #fff;
  font-weight: 700;
  font-size: 15px;
}

.main_product .wrapper .main_right {
  background-size: cover;
  width: 100%;
  background-repeat: no-repeat;
  position: relative;
}

.main_product .wrapper .main_right:hover:after {
  position: absolute;
  content: " ";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: #7bf09885 !important;
}

.main_product .wrapper .main_right .card {
  background: transparent;
  padding-top: 112px;
  padding-bottom: 100px;
  border: none !important;
  margin-left: 65px;
  z-index: 3;
}

@media screen and (max-width: 768px) {
  .main_product .wrapper .main_right .card {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}

.main_product .wrapper .main_right .card .card-title {
  color: #fff;
  font-size: 5vw;
  font-weight: bold;
  padding-bottom: 20px;
  text-transform: uppercase;
}

@media screen and (max-width: 768px) {
  .main_product .wrapper .main_right .card .card-title {
    font-size: 30px !important;
  }
}

.main_product .wrapper .main_right .card .card-text {
  color: #fff;
  font-weight: 700;
  font-size: 15px;
}

.amazing {
  margin-top: 0px;
}

.amazing .amazing_bg {
  /*background-image: url(/theme_xtream/static/src/img/bg-img/bg-5.jpg);*/
  padding-top: 100px;
  background-size: cover;
  width: 100%;
  background-position: center top;
  background-size: cover;
}

@media screen and (max-width: 768px) {
  .amazing .amazing_bg {
    padding-bottom: 50px;
    padding-top: 50px;
  }
}

.amazing .amazing_bg .amazing_content {
  margin: 100px 0;
  margin-left: auto;
  background-color: #3a3a3ab3;
  padding: 50px 50px;
  float: right;
  color: #fff;
}

@media screen and (max-width: 768px) {
  .amazing .amazing_bg .amazing_content {
    float: none;
    margin: auto;
  }
}

.amazing .amazing_bg h2 {
  font-size: 55px;
  font-weight: 600;
  margin-bottom: 20px;
}

@media screen and (max-width: 768px) {
  .amazing .amazing_bg h2 {
    font-size: 30px;
  }
}

.amazing .amazing_bg p {
  padding-bottom: 10px;
  font-size: 15px;
  font-weight: 700;
}

.amazing .amazing_bg .rate {
  margin-bottom: 20px;
}

.amazing .amazing_bg .rate span {
  color: #fff;
  font-size: 30px;
  font-weight: 500;
  text-decoration: line-through;
}

.amazing .amazing_bg .rate span:last-child {
  color: #484bd1;
  font-size: 35px;
  margin-left: 10px;
  font-weight: 600;
  text-decoration: none;
}

.testiomonial {
  margin-top: 0px;
  padding-top: 50px;
  padding-bottom: 50px;
}

.testiomonial .wrapper {
  position: relative;
}

.testiomonial .wrapper h2 {
  text-align: center;
  font-size: 60px;
  font-weight: 700;
  color: #031B09;
  text-transform: uppercase;
  padding-bottom: 30px;
}

@media screen and (max-width: 768px) {
  .testiomonial .wrapper h2 {
    font-size: 30px;
  }
}

.testiomonial .wrapper .testimonial_content {
  padding: 0px 111px;
  padding-top: 0px;
}

@media screen and (max-width: 992px) {
  .testiomonial .wrapper .testimonial_content {
    padding-left: 0;
    padding-right: 0;
  }
}

.testiomonial .wrapper .testimonial_content span {
  color: #484bd1;
  font-size: 30px;
  text-align: center;
  justify-content: center;
  display: flex;
}

.testiomonial .wrapper .testimonial_content .pp {
  margin: 40px 0;
  font-size: 17px;
  line-height: 2;
  font-weight: 700;
  color: #9f9f9f;
  text-align: center;
  padding: 0 100px;
}

@media screen and (max-width: 768px) {
  .testiomonial .wrapper .testimonial_content .pp {
    padding: 0 50px;
  }
}

.testiomonial .wrapper .testimonial_content .img_test {
  padding-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.testiomonial .wrapper .testimonial_content .img_test .wrapper {
  max-width: 75px;
}

.testiomonial .wrapper .testimonial_content .img_test .wrapper img {
  width: 100%;
  border-radius: 50%;
}

.testiomonial .wrapper .testimonial_content .img_test .name {
  margin-left: 20px;
}

.testiomonial .wrapper .testimonial_content .img_test .name p {
  color: #031B09;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.testiomonial .wrapper .testimonial_content .img_test .name span {
  color: #484bd1;
  font-size: 16px;
  font-weight: 700;
}

.testiomonial .owl-carousel button.owl-dot span {
  height: 10px;
  width: 10px;
  border: 1px solid !important;
  border-color: #9f9f9f !important;
  color: #fff;
  border-radius: 50%;
  display: block;
  font-weight: 700;
  margin: 5px;
}

.testiomonial .owl-carousel button.owl-dot.active span {
  background-color: #484bd1 !important;
}

.testiomonial .owl-carousel .owl-dots {
  position: absolute;
  bottom: 165px;
  left: 0px !important;
  transform: rotate(90deg);
  background-color: transparent;
}

@media screen and (max-width: 1000px) {
  .testiomonial .owl-carousel .owl-dots {
    left: 150px;
  }
}

@media screen and (max-width: 768px) {
  .testiomonial .owl-carousel .owl-dots {
    left: 100px;
  }
  .oe_product_cart .oe_product_image{
    height: 300px;
  }
}

@media screen and (max-width: 600px) {
  .testiomonial .owl-carousel .owl-dots {
    left: 75px;
  }
  .oe_product_cart .oe_product_image{
    height: 270px;
  }
}

.discount {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

@media screen and (max-width: 992px) {
  .discount {
    grid-template-columns: 1fr;
  }
}

.discount .dicount_content1 {
  text-align: center;
  color: #fff;
  background-color: #9f9f9f;
  padding: 40px 15px;
}

.discount .dicount_content1 h4 {
  font-size: 25px;
  font-weight: 700;
}

@media screen and (max-width: 992px) {
  .discount .dicount_content1 h4 {
    font-size: 22px;
  }
}

.discount .dicount_content1 a {
  color: #fff;
  font-size: 14px;
  text-decoration: none;
  font-weight: 700;
}

.discount .dicount_content1 a:hover {
  color: #031B09;
}

.discount .dicount_content2 {
  text-align: center;
  color: #fff;
  background-color: #484bd1;
  padding: 40px 15px;
}

.discount .dicount_content2 h4 {
  font-size: 25px;
  font-weight: 700;
}

.discount .dicount_content2 p {
  color: #fff;
  font-size: 14px;
  text-decoration: none;
  font-weight: 700;
}

.discount .dicount_content3 {
  text-align: center;
  color: #fff;
  background-color: #031B09;
  padding: 40px 15px;
}

.discount .dicount_content3 h4 {
  font-size: 25px;
  font-weight: 700;
}

.discount .dicount_content3 p {
  color: #fff;
  font-size: 14px;
  text-decoration: none;
  font-weight: 700;
}

.choose {
  margin: 90px 0;
}

.choose .wrapper {
  padding: 10px 0;
}

.choose .choose_left {
  color: #6c6a74;
  font-size: 14px;
}

.choose .choose_right {
  float: right;
  /* Custom dropdown */
  /*Hiding the select arrow for IE10*/
}

@media screen and (max-width: 768px) {
  .choose .choose_right {
    float: none;
  }
}

.choose .choose_right .custom-dropdown {
  width: 300px;
  border: 1px solid;
  border-color: #dedede;
  border-radius: 5px;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
  transition: 0.5s;
}

.choose .choose_right .custom-dropdown select {
  width: 300px;
  background-color: #ffffff;
  color: #6c6a74;
  font-size: inherit;
  padding: .7em;
  padding-right: 2.5em;
  margin: 0;
  border: none !important;
  text-indent: 0.01px;
  text-overflow: '';
  /*Hiding the select arrow for firefox*/
  /*Hiding the select arrow for chrome*/
  /*Hiding the select arrow default implementation*/
  appearance: none;
}

.choose .choose_right .custom-dropdown select::-ms-expand {
  display: none;
}

.choose .choose_right .custom-dropdown::before,
.choose .choose_right .custom-dropdown::after {
  content: "";
  position: absolute;
  pointer-events: none;
}

.choose .choose_right .custom-dropdown::after {
  /*  Custom dropdown arrow */
  content: "\25BC";
  height: 1em;
  font-size: .625em;
  line-height: 1;
  right: 1.2em;
  top: 50%;
  margin-top: -.5em;
}

.choose .choose_right .custom-dropdown::before {
  /*  Custom dropdown arrow cover */
  width: 2em;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 0 3px 3px 0;
  background-color: rgba(0, 0, 0, 0.2);
}

.choose .choose_right .custom-dropdown::after {
  color: rgba(0, 0, 0, 0.6);
}

.choose .choose_right .custom-dropdown select[disabled] {
  color: rgba(0, 0, 0, 0.25);
}



.oe_product_cart .o_wsale_product_btn .btn{
  background-color: white !important;
  color: #484bd1 !important;
  border-color: #484bd1 !important;
  border-radius: 100% !important;
}

.oe_product_cart .o_wsale_product_btn .btn:hover{
  background-color: transparent !important;
  color: #484bd1 !important;
  border-color: #031B09 !important;
  border-radius: 100% !important;
}

span[data-oe-type="monetary"],
span[data-oe-type="monetary"] .oe_currency_value{
  color: #9f9f9f;
  font-size: 16px;
  font-weight: lighter;
  margin-top: 20px;
  margin-bottom: 5px;
}

#cart_total #order_total .text-xl-right .monetary_field,
#cart_total #order_total .text-xl-right .monetary_field .oe_currency_value{
  font-size: 20px;
  margin-top: 20px;
  margin-bottom: 5px;
}

.badge-secondary {
    color: #9f9f9f !important;
    font-size: 20px !important;
    background-color: #000 !important;
}
.input-group > .custom-select:not(:first-child), .input-group > .form-control:not(:first-child) {
    border-color: black !important;
}

.input-group-prepend > .btn, .input-group-append > .btn {
    padding: 0.250rem 0.75rem !important;
}

.Shop_product {
  margin-top: 0px;
  /* Add padding BETWEEN each column */
  /* Create three equal columns that floats next to each other */
  /* Clear floats after rows */
  /* Content */
  /* The "show" class is added to the filtered elements */
  /* Style the buttons */
}

.Shop_product .main {
  padding-top: 30px;
  margin: auto;
}

.Shop_product .main h2 {
  text-align: center;
  font-size: 60px;
  font-weight: 700;
  color: #031B09;
  text-transform: uppercase;
  padding-bottom: 30px;
}

@media screen and (max-width: 768px) {
  .Shop_product .main h2 {
    font-size: 30px;
  }
}

.Shop_product .main .categories {
  display: flex;
  justify-content: center;
}

@media screen and (max-width: 768px) {
  .Shop_product .main .categories {
    display: block;
  }
}

.Shop_product h1 {
  font-size: 50px;
  word-break: break-all;
}

.Shop_product .row {
  margin: 10px -16px;
}

.Shop_product .row,
.Shop_product .row > .column {
  padding: 8px;
}

.Shop_product .column {
  float: left;
  width: 33.33%;
  display: none;
  /* Hide all elements by default */
}

.Shop_product .row:after {
  content: "";
  display: table;
  clear: both;
}

.Shop_product .content {
  padding: 5px 0;
}

.Shop_product .content .img_zoom {
  overflow: hidden;
}

.Shop_product .content .wrapper {
  max-width: 330px;
  position: relative;
}

.Shop_product .content .wrapper:hover .img_details {
  position: absolute;
  left: 44%;
  display: block;
  bottom: 45%;
  z-index: 3;
}

@media screen and (max-width: 992px) {
  .Shop_product .content .wrapper:hover .img_details {
    left: 35%;
    bottom: 43%;
  }
}

@media screen and (max-width: 992px) {
  .Shop_product .content .wrapper:hover .img_details {
    left: 44%;
    bottom: 43%;
  }
}

.Shop_product .content .wrapper:hover .img_details i {
  font-size: 25px;
  color: #fff;
  padding: 4px 2px;
}

@media screen and (max-width: 992px) {
  .Shop_product .content .wrapper:hover .img_details i {
    font-size: 12px;
  }
}

.Shop_product .content .wrapper:hover:after {
  position: absolute;
  content: " ";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: #00000054 !important;
}

.Shop_product .content .wrapper:before {
  position: absolute;
  content: " ";
  display: block;
  top: 50%;
  left: 50%;
}

@media screen and (max-width: 576px) {
  .Shop_product .content .wrapper {
    max-width: none;
  }
}

.Shop_product .content .wrapper .img_details {
  display: none;
}

.Shop_product .content .wrapper img {
  width: 100%;
}

.Shop_product .content p {
  color: #9f9f9f;
  font-size: 25px;
  font-weight: lighter;
  margin-top: 20px;
  margin-bottom: 5px;
}

.Shop_product .content h6 {
  color: #031B09;
  line-height: 1.5;
  font-weight: 400;
  font-size: 15px;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.Shop_product .content a {
  font-size: 14px;
  font-weight: 700;
  color: #031B09;
  text-decoration: none;
}

.Shop_product .content a:hover {
  color: #031B09;
}

.Shop_product .content #zoomIn {
  transform: scale(1);
  transition: .3s ease-in-out;
}

.Shop_product .content #zoomIn:hover {
  transform: scale(1.05);
  border-radius: 6px 6px 0px 0px;
}

.Shop_product .show {
  display: block;
}

.Shop_product .btn {
  border: none;
  outline: none;
  padding: 12px 16px;
  background-color: white;
  cursor: pointer;
  color: #9f9f9f;
  font-weight: 700;
}

.Shop_product .btn:hover {
  background-color: #ddd;
}

.Shop_product .btn.active {
  color: #031B09;
}

.Shop_product .modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0;
  outline: 0;
  margin: auto;
}

@media screen and (max-width: 768px) {
  .Shop_product .modal-content {
    margin: auto;
    max-width: 65%;
  }
}

@media screen and (max-width: 576px) {
  .Shop_product .modal-content {
    margin: auto;
    max-width: 80%;
  }
}

.Shop_product .modal-dialog {
  max-width: 660px;
  margin: 1.75rem auto;
}

@media screen and (max-width: 768px) {
  .Shop_product .modal-dialog {
    margin: 50px 50px;
  }
}

.Shop_product .modal .model-body {
  padding: 20px 25px;
}

.Shop_product .modal .wrapper {
  max-width: 100% !important;
}

.Shop_product .modal .wrapper img {
  width: 100%;
}

.Shop_product .modal .model_details h4 {
  color: #031B09;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 10px;
}

.Shop_product .modal .model_details .model_rate ul {
  padding-left: 0;
  display: flex;
}

.Shop_product .modal .model_details .model_rate ul span {
  color: #ffcd07;
}

.Shop_product .modal .model_details .price {
  color: #031B09;
  font-size: 20px;
  font-weight: 700;
}

.Shop_product .modal .model_details .price span {
  margin-left: 5px;
  font-weight: 700;
  font-size: 16;
  color: #6c6a74;
  text-decoration: line-through;
}

.Shop_product .modal .model_details p {
  color: #031B09;
  line-height: 24px;
}

.Shop_product .modal .model_details a {
  text-decoration: underline;
}

.Shop_product .modal .model_details .product_count {
  margin-top: 30px;
  display: flex;
}

@media screen and (max-width: 412px) {
  .Shop_product .modal .model_details .product_count {
    display: grid !important;
    grid-row-gap: 20px !important;
  }
}

.Shop_product .modal .model_details .product_count #myform {
  margin-right: 10px;
  text-align: center;
  display: flex;
  width: 100px;
}

.Shop_product .modal .model_details .product_count .qty {
  width: 40px;
  height: 25px;
  text-align: center;
  border: none;
}

.Shop_product .modal .model_details .product_count input.qtyplus {
  width: 25px;
  height: 25px;
  background-color: transparent;
  border: none;
}

.Shop_product .modal .model_details .product_count input.qtyminus {
  width: 25px;
  height: 25px;
  background-color: transparent;
  border: none;
}

.Shop_product .modal .model_details .product_count .icons {
  margin-left: 15px;
}

.Shop_product .modal .model_details .product_count .icons span {
  background: #484bd1;
  color: #fff;
  font-size: 16px;
  padding: 9px 10px;
}

.Shop_product .modal .model_details .product_count .icons span:hover {
  background-color: #000000;
}

.Shop_product .modal .model_details .product_count .icons span:last-child {
  background-color: #00bcd4 !important;
  margin-left: 15px;
}

.Shop_product .modal .model_details .share {
  font-size: 14px;
  font-weight: 400;
  padding-top: 20px;
}

.Shop_product .modal .model_details .footer_icon {
  padding-top: 5px;
}

.Shop_product .modal .model_details .footer_icon a {
  color: #9f9f9f;
  margin-right: 15px;
}

.Shop_product .modal .model_details .footer_icon a:hover {
  color: #000000;
}

.products_pager .pagination .page-item.active .page-link {
  color: #484bd1;
  background-color: transparent;
  border-color: #484bd1;
}

.products_pager .pagination .page-item .page-link {
  color: #9f9f9f;
  color: #9f9f9f;
  border: 0;
  font-size: 14px;
  border: 2px solid;
  margin-left: 2px;
  border-radius: 0;
  border-color: #9f9f9f;
  box-shadow: none;
}

.products_pager .pagination .page-item .page-link:hover {
  color: #484bd1;
  border-color: #484bd1 !important;
}

.product_preview {
  margin-top: 90px;
}

.product_preview .breadcrumb {
  background: transparent;
  margin-top: 40px;
  padding-left: 0;
}

.product_preview .breadcrumb .breadcrumb-item a {
  color: #031B09;
  text-decoration: none;
}

.product_preview .back_to_page a {
  font-size: 12px;
  color: #031B09;
  text-decoration: none;
}

.product_preview .back_to_page span {
  margin-right: 5px;
  font-size: 11px;
}

.product_preview .product_p {
  margin-top: 90px;
}

.product_preview .product_p .wrapper {
  position: relative;
}

@media screen and (max-width: 768px) {
  .product_preview .product_p .wrapper .preview_details {
    margin-top: 70px;
  }
}

.product_preview .product_p .wrapper .preview_details h5 {
  color: #000000;
  font-size: 25px;
}

.product_price_xtream p{
  font-size: 25px;
  font-weight: 700;
  margin-top: 10px;
  color: #031B09;
}
.product_price_xtream b{
  font-size: 25px;
  font-weight: 700;
  margin-top: 10px;
  color: #031B09;
}
.product_price_xtream span{
  font-size: 25px;
  font-weight: 700;
  margin-top: 10px;
  color: #031B09;
}
.product_preview .product_p .wrapper .preview_details .price {
  font-size: 25px;
  font-weight: 700;
  margin-top: 10px;
  color: #031B09;
}

.product_preview .product_p .wrapper .preview_details .stock {
  font-size: 12px;
  line-height: 1.5;
}

.product_preview .product_p .wrapper .preview_details .stock span {
  font-size: 12px;
  line-height: 1.5;
  color: #9f9f9f;
}

.product_preview .product_p .wrapper .preview_details .rating {
  display: flex;
  padding-left: 0;
  padding-top: 10px;
}

.product_preview .product_p .wrapper .preview_details .rating li a {
  margin-right: 4px;
  color: #ff9800;
}

.product_preview .product_p .wrapper .preview_details .rating li a span {
  font-size: 13px;
}

.product_preview .product_p .wrapper .preview_details .size_wrapper h4 {
  color: #031B09;
  font-size: 14px;
  font-weight: 700;
  padding-top: 15px;
}

.product_preview .product_p .wrapper .preview_details .size_wrapper .size {
  display: flex;
  padding-left: 0;
}

.product_preview .product_p .wrapper .preview_details .size_wrapper .size a {
  text-decoration: none;
}

.product_preview .product_p .wrapper .preview_details .size_wrapper .size span {
  font-size: 14px;
  color: #000000;
  display: block;
  padding: 8px 12px;
  font-size: 13px;
  background-color: transparent;
  font-weight: 700;
  margin-right: 12px;
  border: 2px solid;
  border-color: #031B09;
}

.product_preview .product_p .wrapper .preview_details .size_wrapper .size span:hover {
  background-color: #484bd1 !important;
  color: #fff;
  border-color: #484bd1 !important;
}

.product_preview .product_p .wrapper .preview_details .product_quantity {
  display: flex;
  margin-top: 45px;
}

.product_preview .product_p .wrapper .preview_details .product_quantity #myform {
  text-align: center;
  border: 2px solid #ccc;
  display: flex;
  border-radius: 0px;
  width: 100px;
  justify-content: space-around;
  align-items: center;
}

.product_preview .product_p .wrapper .preview_details .product_quantity #myform .wrapper_q {
  display: block !important;
}

.product_preview .product_p .wrapper .preview_details .product_quantity .qty {
  width: 40px;
  height: 15px;
  text-align: center;
  border: none;
}

.product_preview .product_p .wrapper .preview_details .product_quantity input.qtyplus {
  width: 25px;
  border: none;
  background-color: transparent;
  display: block !important;
}

.product_preview .product_p .wrapper .preview_details .product_quantity input.qtyminus {
  width: 25px;
  border: none;
  background-color: transparent;
}

.collpase_wrapper {
  margin-top: 20px;
}

.collpase_wrapper .accordion:nth-child(2) .card {
  border-bottom-color: #484bd1;
}

.collpase_wrapper .accordion .card {
  border-bottom-color: transparent;
  background-color: transparent;
  border-radius: 0;
  border-left-color: transparent;
  border-right-color: transparent;
}

.collpase_wrapper .accordion .card .card-header {
  background-color: transparent;
  padding: 0px 0;
}

.collpase_wrapper .accordion .card .card-header .btn-link,
.collpase_wrapper .accordion .card .card-header .collapsed {
  color: #000000;
  font-size: 18px;
  text-decoration: none;
  font-weight: 700;
  text-transform: uppercase;
}
.main_product .card-body {
  background-color: transparent !important;
}

.collpase_wrapper .accordion .card .collapse .card-body {
  color: #9f9f9f;
}

.product_preview .product_p .wrapper .preview_details .collpase_wrapper {
  margin-top: 0px;
}

.product_preview .product_p .wrapper .preview_details .collpase_wrapper .accordion:nth-child(2) .card {
  border-bottom-color: #484bd1;
}

.product_preview .product_p .wrapper .preview_details .collpase_wrapper .accordion .card {
  border-bottom-color: transparent;
  background-color: transparent;
  border-radius: 0;
  border-left-color: transparent;
  border-right-color: transparent;
}

.product_preview .product_p .wrapper .preview_details .collpase_wrapper .accordion .card .card-header {
  background-color: transparent;
  padding: 25px 0;
}

.product_preview .product_p .wrapper .preview_details .collpase_wrapper .accordion .card .card-header .btn-link,
.product_preview .product_p .wrapper .preview_details .collpase_wrapper .accordion .card .card-header .collapsed {
  color: #000000;
  font-size: 18px;
  text-decoration: none;
  font-weight: 700;
  text-transform: uppercase;
}
.main_product .card-body {
  background-color: transparent !important;
}

.product_preview .product_p .wrapper .preview_details .collpase_wrapper .accordion .card .collapse .card-body {
  color: #9f9f9f;
}

.product_preview .trending_body1 .outer {
  margin: 0 auto;
}

.product_preview .trending_body1 #big .item {
  width: 100%;
  display: block;
}

@media screen and (max-width: 576px) {
  .product_preview .trending_body1 #thumbs {
    display: none;
  }
}

.product_preview .trending_body1 #thumbs .item {
  background: #c9c9c9;
  height: 70px;
  line-height: 70px;
  padding: 0px;
  margin: 2px;
  color: #fff;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}

.product_preview .trending_body1 #thumbs .item h1 {
  font-size: 18px;
}

.product_preview .trending_body1 .owl-theme .owl-nav [class*="owl-"] {
  transition: all 0.3s ease;
}

.product_preview .trending_body1 .owl-theme .owl-nav [class*="owl-"].disabled:hover {
  background-color: #d6d6d6;
}

.product_preview .trending_body1 #big.owl-theme {
  position: relative;
}

.product_preview .trending_body1 #big.owl-theme .owl-next,
.product_preview .trending_body1 #big.owl-theme .owl-prev {
  background: transparent;
  width: 22px;
  line-height: 40px;
  height: 40px;
  margin-top: -20px;
  position: absolute;
  text-align: center;
  top: 50%;
}

.product_preview .trending_body1 #big.owl-theme .owl-prev {
  left: 10px;
}

.product_preview .trending_body1 #big.owl-theme .owl-next {
  right: 10px;
}

.demo_h {
  text-align: center;
  font-size: 60px;
  font-weight: 700;
  color: #031B09;
  text-transform: uppercase;
  padding-bottom: 30px;
}

@media screen and (max-width: 768px) {
  .demo_h {
    font-size: 30px;
  }
}

.cart {
  margin-top: 90px;
}

.cart .table_wrapper .table-responsive {
  overflow-x: auto;
}

.cart .table_wrapper .table {
  overflow-x: auto;
}

.cart .table_wrapper .table thead {
  background-color: #f4f2f8;
}

.cart .table_wrapper .table tbody .cart_img {
  display: flex;
  align-items: center;
}

.cart .table_wrapper .table tbody .cart_img .wrapper {
  max-width: 150px;
}

.cart .table_wrapper .table tbody .cart_img .wrapper img {
  width: 100%;
}

.cart .table_wrapper .table tbody .cart_img h6 {
  color: #031B09;
  font-size: 23px;
  padding-left: 20px;
}

@media screen and (max-width: 600px) {
  .cart .table_wrapper .table tbody .cart_img h6 {
    font-size: 14px;
    padding-left: 10px;
  }
}

.cart .table_wrapper .table tbody td {
  vertical-align: middle;
}

.cart .table_wrapper .table tbody .cart_q #myform {
  text-align: center;
  padding: 5px;
  border: 1px solid #ccc;
  display: flex;
  margin: 2%;
  width: 95px;
}

.cart .table_wrapper .table tbody .cart_q .qty {
  width: 40px;
  height: 25px;
  text-align: center;
  border: none;
}

.cart .table_wrapper .table tbody .cart_q input.qtyplus {
  width: 25px;
  height: 25px;
  background-color: transparent;
  border: none;
}

.cart .table_wrapper .table tbody .cart_q input.qtyminus {
  width: 25px;
  height: 25px;
  background-color: transparent;
  border: none;
}

.cart .c_buttons {
  margin-top: 30px;
}

.cart .cart_bottom {
  margin-top: 70px;
}

.cart .cart_bottom .coupen {
  padding-top: 30px;
}

.cart .cart_bottom .coupen .hb h5 {
  color: #031B09;
  font-size: 23px;
  font-weight: 700;
}

.cart .cart_bottom .coupen .hb p {
  color: #9f9f9f;
}

.cart .cart_bottom .coupen .input-group {
  width: 100%;
  height: 50px;
  border-radius: 0;
  margin-top: 70px;
}

.cart .cart_bottom .coupen .input-group .form-control {
  border-radius: 0;
  height: 50px;
}

.cart .cart_bottom .coupen .input-group .input-group-text {
  border-radius: 0;
  background-color: #484bd1;
  padding: 0 20px;
  color: #fff;
  border: 1px solid;
  font-weight: 700;
}

.cart .cart_bottom .coupen .form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #484bd1;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.cart .cart_bottom .coupen .radio_wrapper {
  margin-top: 35px;
}

.cart .cart_bottom .coupen .custom-control {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  margin-right: 20px;
}

.cart .cart_bottom .coupen .custom-control p {
  color: #000000;
}

.cart .cart_bottom .coupen .cart-total-chart {
  padding-left: 0;
  margin-top: 35px;
}

.cart .cart_bottom .coupen .cart-total-chart li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  margin-right: 20px;
}

.checkout {
  margin-top: 90px;
}

.checkout .checkout_left .billing h3 {
  font-weight: 600;
  color: #031B09;
  text-transform: uppercase;
}

.checkout .checkout_left .billing p {
  color: #6c6a74;
  margin-top: 15px;
}

.checkout_autoformat .form-row .form-control {
  display: block;
  width: 100%;
  height: calc(2em + .85rem + 3px) !important;
  padding: .375rem .75rem !important;
  font-size: 1rem !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #495057 !important;
  background-color: #e3e3e3 !important;
  background-clip: padding-box !important;
  border: 1px solid !important;
  border-color: transparent !important;
  border-radius: 0 !important;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out !important;
}
.checkout_autoformat .form-row .form-control:focus {
  color: #495057 !important;
  background-color: #fff !important;
  border-color: #484bd1 !important;
  outline: 0 !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}


.checkout .checkout_left .form-control {
  display: block;
  width: 100%;
  height: calc(2em + .85rem + 3px);
  padding: .375rem .75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #e3e3e3;
  background-clip: padding-box;
  border: 1px solid;
  border-color: transparent !important;
  border-radius: 0;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.checkout .checkout_left .form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #484bd1 !important;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.checkout .checkout_left .custom-select {
  display: inline-block;
  width: 100%;
  height: calc(2em + .85rem + 3px);
  padding: .375rem 1.75rem .375rem .75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  border: 1px solid #9f9f9f;
  border-radius: 0;
  appearance: none;
  box-shadow: none;
}

.checkout .checkout_left .card {
  border: none;
}

.checkout .checkout_left .card .card-body .md-form {
  color: #6c6a74;
}

.checkout .checkout_left .card .card-body .md-form .lable {
  color: #000000;
}

.checkout .checkout_left .card .card-body .custom-control-input:checked ~ .custom-control-label::before {
  color: #e9c939;
  border-color: #e9c939;
  background-color: #e9c939;
  outline: none;
}

.checkout .checkout_left .card .card-body .form-check-input:checked ~ .form-check-label::before {
  color: #e9c939 !important;
  border-color: #e9c939 !important;
  background-color: #e9c939 !important;
  content: "";
}

.checkout .checkout_left .card .card-body .input[type="checkbox"]:before, .checkout .checkout_left .card .card-body input[type="radio"]:before {
  position: absolute;
  top: .25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  background-color: #8a1717;
  border: #3e71a5 solid 1px;
}

.checkout .checkout_right .order h3 {
  font-weight: 600;
  color: #031B09;
}

.checkout .checkout_right .order .subhead {
  color: #6c6a74;
  padding-top: 10px;
}

.checkout .checkout_right .order .wrapper {
  padding-left: 30px;
}

.checkout .checkout_right .order ul {
  padding-top: 20px;
  padding-left: 0;
}

.checkout .checkout_right .order ul li {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding-bottom: 15px;
}

.checkout .checkout_right .order ul li span {
  padding-right: 30px;
}

.checkout .checkout_right .order ul li .nn {
  color: #000000;
}

.checkout .checkout_right .order .payment label {
  color: #000000;
}

.checkout .checkout_right .order .payment [type="radio"]:checked,
.checkout .checkout_right .order .payment [type="radio"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

.checkout .checkout_right .order .payment [type="radio"]:checked + label,
.checkout .checkout_right .order .payment [type="radio"]:not(:checked) + label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  line-height: 20px;
  display: inline-block;
  color: #666;
}

.checkout .checkout_right .order .payment [type="radio"]:checked + label:before,
.checkout .checkout_right .order .payment [type="radio"]:not(:checked) + label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid;
  border-color: #dedede;
  border-radius: 100%;
  background: #fff;
}

.checkout .checkout_right .order .payment [type="radio"]:checked + label:after,
.checkout .checkout_right .order .payment [type="radio"]:not(:checked) + label:after {
  content: '';
  width: 11px;
  height: 12px;
  background: #e9c939;
  position: absolute;
  top: 3px;
  left: 3px;
  border-radius: 100%;
  transition: all 0.2s ease;
}

.checkout .checkout_right .order .payment [type="radio"]:not(:checked) + label:after {
  opacity: 0;
  transform: scale(0);
}

.checkout .checkout_right .order .payment [type="radio"]:checked + label:after {
  opacity: 1;
  transform: scale(1);
}

.checkout .checkout_right .order .order_text {
  font-style: italic;
  color: #6c6a74;
  margin-top: 55px;
  margin-bottom: 20px;
}

.contact {
  margin-top: 0px;
  margin-bottom: 0px;
}

.contact .row .form-group{
  width: 100%;
}
.contact .row .form-group .form-field{
  width: 100%;
}

.contact .contact_left .name h3 {
  font-weight: 700;
  color: #031B09;
  text-transform: uppercase;
}

.contact .contact_left .name p {
  color: #6c6a74;
}

.contact .contact_left .name .contact-form {
  margin-top: 70px;
}

.contact .contact_left .name .contact-form .form-control {
  display: block;
  width: 100%;
  height: calc(2.5em + .75rem + 2px);
  padding: .375rem .75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-color: transparent;
  border-bottom-color: #dedede;
  border-radius: 0;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.contact .contact_left .name .contact-form .form-control:focus {
  color: #495057;
  background-color: #fff;
  border-bottom-color: #484bd1 !important;
  outline: 0;
  box-shadow: none;
}

.contact .contact_left .name .contact-form .input-block {
  margin-bottom: 30px;
}

.contact .contact_left .name .contact-form .input-block label {
  color: #6c6a74;
}

@media screen and (max-width: 992px) {
  .contact .contact_right {
    margin-top: 5px;
  }
}

@media (max-width: 500px) {
div.products_header .o_sortby_dropdown {
    display: none;
}

}

.contact .contact_right .c_info {
  margin-bottom: 10px;
}

.contact .contact_right h3 {
  margin-bottom: 10px;
  text-transform: uppercase;
  font-weight: 700;
  color: #031B09;
}

.contact .contact_right .phone {
  padding-top: 5px;
  padding-bottom: 5px;
}

.contact .contact_right .phone span {
  padding-right: 20px;
}

.contact .contact_right .phone a {
  color: #6c6a74;
  text-decoration: none;
}

.contact .contact_right .phone a:hover {
  color: #e9c939 !important;
}

.map .mapouter {
  position: relative;
  text-align: right;
  height: 500px;
  widows: 100%;
}

.map .mapouter .mapouter {
  overflow: hidden;
  background: none !important;
  height: 100%;
  width: 100%;
}
/*# sourceMappingURL=style.css.map */

#slider-data-range {
  margin-bottom: 30px;
  background-color: #000000;
  border: none;
  height: 8px;
}

.price-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.price-range:focus {
  outline: 0;
}
.price-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #000, 0 0 0 0.25rem #000000;
}
.price-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #000, 0 0 0 0.25rem #000000;
}
.price-range::-moz-focus-outer {
  border: 0;
}
.price-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #000000;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .price-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.price-range::-webkit-slider-thumb:active {
  background-color: #000000;
}
.price-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #000000;
  border-color: transparent;
  border-radius: 1rem;
}
.price-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #000000;
  border: 0;
  border-radius: 1rem;
  -moz-appearance: none;
  appearance: none;
}

.price-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #000000;
  border-color: transparent;
  border-radius: 1rem;
}
.price-range:disabled {
  pointer-events: none;
}
.multirange-wrapper input[type="range"][multiple].multirange.ghost::-moz-range-track {
  background: linear-gradient(to right, transparent var(--low),#484bd1 var(--low) var(--high), transparent 0) no-repeat 50%/100% 100%;
}

div.footer_content .subscribe span#basic-text1 {
    height: -webkit-fill-available;
    background-color: #000000;
}
div.contact_left form#contactus_form a.btn-contact {
    justify-content: center;
    display: flex;
    align-items: center;
    width: 35%;
}
div.o_wsale_products_main_row .products_pager .pagination .page-item .page-link {
    border: none;
    height: 27px;
}
div.products_header .o_sortby_dropdown a.dropdown-toggle {
    border-radius: 5px !important;
    border: none !important;
    height: 20px;
    background-color: #F3F2F2 !important;
}
div.products_header .o_sortby_dropdown {
    background-color: #F3F2F2 !important;
    border-radius: 5px !important;
}
div#product_details div#product_option_block i.fa-heart-o {
    margin-left: 10px;
}
div#product_details .input-group > .form-control:not(:first-child) {
    border: 2px solid !important;
    border-color: #484bd1 !important;
}
div#cart_products div.o_cart_product .input-group > .form-control:not(:first-child) {
    border: 2px solid !important;
    border-color: #484bd1 !important;
}
div.o_portal_details div.clearfix button.btn-primary {
    height:35px;
    line-height: 0;
}
div.o_website_sale_checkout div.oe_cart div.alert-warning a.alert-warning {
    border: none !important;
}
div.o_footer_copyright span.o_footer_copyright_name {
    color: black !important;
}
div.o_homepage_editor_welcome_message h2 b {
    font-size: 2.5rem;
}
div.oe_cart a.js_edit_address {
    border: none !important;
}
div#products_grid div.o_wsale_product_information div.o_wsale_product_btn .btn {
    height: 40px;
}
div.alert-success a.btn-link:hover {
    color: white !important;
}
.btn {
    font-size: 12px;
}
div#modal_4 footer.modal-footer button {
    height: 35px;
}
div#modal_4 .oe_price, div#modal_4 .oe_currency_value {
    color: #9f9f9f;
    font-size: 20px;
    font-weight: lighter;
    margin-top: 20px;
    margin-bottom: 5px;
}
div#modal_4 .modal-content {
    width: 132% !important;
}
a.btn-link alert-warning ps-2 {
    color: #997404 !important;
}
.btn-outline-primary {
    background-color: transparent !important;
    color: #484bd1 !important;
}
.oe_advanced_configurator_modal {
    overflow-x: auto !important;
    width: 100% !important;
}

.text-primary {
    color: #484bd1 !important;
}

.css_attribute_color.active, .form-check-input:checked  {
    border-color: #484bd1 !important;
}
.form-check-input:checked {
    background-color: #484bd1 !important;
}

.navbar-nav .nav-link.active, .navbar-nav .nav-link.show {
    color: var(--navbar-active-color) important;
}

.btn {
    font-size: 14px !important;
}

.oe_search_box {
    padding-right: 5px !important;
}