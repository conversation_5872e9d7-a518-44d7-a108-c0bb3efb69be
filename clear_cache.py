#!/usr/bin/env python3
"""
Script para limpar cache do Odoo após modificações no tema
"""

import os
import sys
import shutil
import glob

def clear_odoo_cache():
    """Limpa o cache do Odoo"""
    print("🧹 Limpando cache do Odoo...")
    
    # Diretórios de cache comuns do Odoo
    cache_dirs = [
        'addons/theme_ryrox/__pycache__',
        'addons/theme_ryrox/*/__pycache__',
        'addons/theme_ryrox/*/*/__pycache__',
    ]
    
    # Remove arquivos .pyc
    pyc_files = glob.glob('addons/theme_ryrox/**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"  ✅ Removido: {pyc_file}")
        except Exception as e:
            print(f"  ❌ Erro ao remover {pyc_file}: {e}")
    
    # Remove diretórios __pycache__
    pycache_dirs = glob.glob('addons/theme_ryrox/**/__pycache__', recursive=True)
    for cache_dir in pycache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"  ✅ Removido diretório: {cache_dir}")
        except Exception as e:
            print(f"  ❌ Erro ao remover {cache_dir}: {e}")
    
    print("✨ Cache limpo com sucesso!")

def validate_xml_files():
    """Valida arquivos XML do tema"""
    print("🔍 Validando arquivos XML...")
    
    xml_files = glob.glob('addons/theme_ryrox/views/**/*.xml', recursive=True)
    
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Verificações básicas
            if not content.strip().startswith('<?xml'):
                print(f"  ⚠️  {xml_file}: Não inicia com declaração XML")
            
            if not content.strip().endswith('</odoo>'):
                print(f"  ⚠️  {xml_file}: Não termina com </odoo>")
            
            # Verifica se tem conteúdo
            if len(content.strip()) < 50:
                print(f"  ⚠️  {xml_file}: Arquivo muito pequeno")
            
            print(f"  ✅ {xml_file}: OK")
            
        except Exception as e:
            print(f"  ❌ Erro ao validar {xml_file}: {e}")
    
    print("✅ Validação XML concluída!")

def show_restart_instructions():
    """Mostra instruções para reiniciar o Odoo"""
    print("\n🔄 Para aplicar as alterações:")
    print("1. Pare o servidor Odoo (Ctrl+C)")
    print("2. Reinicie com: python odoo-bin -u theme_ryrox")
    print("3. Ou use: python odoo-bin --dev=reload")
    print("\n💡 Comandos úteis:")
    print("   - Atualizar módulo: -u theme_ryrox")
    print("   - Modo desenvolvimento: --dev=reload")
    print("   - Limpar cache: --dev=reload,qweb,werkzeug,xml")

if __name__ == "__main__":
    print("🚀 Limpeza de Cache - Tema Ryrox")
    print("=" * 40)
    
    clear_odoo_cache()
    validate_xml_files()
    show_restart_instructions()
    
    print("\n✨ Processo concluído!")
