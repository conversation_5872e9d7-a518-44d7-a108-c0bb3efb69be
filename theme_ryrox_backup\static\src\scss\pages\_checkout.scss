.checkout {
    margin-top: 90px;
    .checkout_left {
        .billing {
            h3 {
                font-weight: 600;
                color: $color-brand;
                text-transform: uppercase;
            }
            p {
                color: $color-grey;
                margin-top: 15px;
            }
        }
        .form-control {
            display: block;
            width: 100%;
            height: calc(2em + 0.85rem + 3px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #e3e3e3;
            background-clip: padding-box;
            border: 1px solid;
            border-color: transparent !important;
            border-radius: 0;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        .form-control:focus {
            color: #495057;
            background-color: #fff;
            border-color: $color-brand2 !important;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .custom-select {
            display: inline-block;
            width: 100%;
            height: calc(2em + 0.85rem + 3px);
            padding: 0.375rem 1.75rem 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            vertical-align: middle;
            border: 1px solid $color-font;
            border-radius: 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            box-shadow: none;
        }
        .card {
            border: none;
            .card-body {
                .md-form {
                    color: $color-grey;
                    .lable {
                        color: $color-black;
                    }
                }
                .custom-control-input:checked ~ .custom-control-label::before {
                    color: $color-hover;
                    border-color: $color-hover;
                    background-color: $color-hover;
                    outline: none;
                }
                .form-check-input:checked ~ .form-check-label::before {
                    color: $color-hover !important;
                    border-color: $color-hover !important;
                    background-color: $color-hover !important;
                    content: "";
                }
                .input[type="checkbox"],
                input[type="radio"] {
                    &:before {
                        position: absolute;
                        top: 0.25rem;
                        left: -1.5rem;
                        display: block;
                        width: 1rem;
                        height: 1rem;
                        pointer-events: none;
                        background-color: rgb(138, 23, 23);
                        border: #3e71a5 solid 1px;
                    }
                }
            }
        }
    }
    .checkout_right {
        .order {
            h3 {
                font-weight: 600;
                color: $color-brand;
            }
            .subhead {
                color: $color-grey;
                padding-top: 10px;
            }
            .wrapper {
                padding-left: 30px;
            }
            ul {
                padding-top: 20px;
                padding-left: 0;
                li {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;
                    padding-bottom: 15px;
                    span {
                        padding-right: 30px;
                    }
                    .nn {
                        color: $color-black;
                    }
                }
            }
            .payment {
                label {
                    color: $color-black;
                }
                [type="radio"]:checked,
                [type="radio"]:not(:checked) {
                    position: absolute;
                    left: -9999px;
                }
                [type="radio"]:checked + label,
                [type="radio"]:not(:checked) + label {
                    position: relative;
                    padding-left: 28px;
                    cursor: pointer;
                    line-height: 20px;
                    display: inline-block;
                    color: #666;
                }
                [type="radio"]:checked + label:before,
                [type="radio"]:not(:checked) + label:before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 18px;
                    height: 18px;
                    border: 1px solid;
                    border-color: $color-border;
                    border-radius: 100%;
                    background: #fff;
                }
                [type="radio"]:checked + label:after,
                [type="radio"]:not(:checked) + label:after {
                    content: "";
                    width: 11px;
                    height: 12px;
                    background: $color-hover;
                    position: absolute;
                    top: 3px;
                    left: 3px;
                    border-radius: 100%;
                    -webkit-transition: all 0.2s ease;
                    transition: all 0.2s ease;
                }
                [type="radio"]:not(:checked) + label:after {
                    opacity: 0;
                    -webkit-transform: scale(0);
                    transform: scale(0);
                }
                [type="radio"]:checked + label:after {
                    opacity: 1;
                    -webkit-transform: scale(1);
                    transform: scale(1);
                }
            }
            .order_text {
                font-style: italic;
                color: $color-grey;
                margin-top: 55px;
                margin-bottom: 20px;
            }
        }
    }
}
